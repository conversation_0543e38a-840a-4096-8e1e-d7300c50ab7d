import { getRandomSuggestions } from '@/ai/image/lib/suggestions';
import { ImagePlayground } from '@/components/Customize/generator';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { Routes } from '@/routes';
import { useTranslations } from 'next-intl';

/**
 * AI Image Generation page in Dashboard
 */
export default function GenerateImagesPage() {
  const t = useTranslations('Dashboard.dashboard');
  const tDashboard = useTranslations('HomePage.dashboard.generateImages');

  const breadcrumbs = [
    {
      label: t('title'),
      href: Routes.Dashboard,
    },
    {
      label: tDashboard('breadcrumb'),
      isCurrentPage: true,
    },
  ];

  return (
    <>
      <DashboardHeader breadcrumbs={breadcrumbs} />

      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col">
          {/* 移除额外的 padding 和 margin，让 ImagePlayground 组件自己处理布局 */}
          <ImagePlayground suggestions={getRandomSuggestions(5)} />
        </div>
      </div>
    </>
  );
}
