/**
 * 测试修复后的翻译功能
 */
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 模拟修复后的 isTextPrimarylyEnglish 函数
function isTextPrimarylyEnglish(text) {
  // 移除标点符号、数字和空格，只保留字母
  const letters = text.replace(/[^\p{L}]/gu, '');
  if (letters.length === 0) return true; // 没有字母的情况默认为英文
  
  // 计算英文字母的比例
  const englishLetters = letters.match(/[a-zA-Z]/g);
  const englishRatio = englishLetters ? englishLetters.length / letters.length : 0;
  
  // 检查是否包含中文、日文、韩文等字符
  const cjkPattern = /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/;
  const hasCJK = cjkPattern.test(text);
  
  // 如果包含中日韩文字，即使英文比例高也需要翻译
  if (hasCJK) {
    console.log(`[Translation] Text contains CJK characters, will translate despite English ratio: ${englishRatio}`);
    return false;
  }
  
  // 如果英文字母占比超过90%，认为是英文文本
  const isEnglish = englishRatio > 0.9;
  console.log(`[Translation] English ratio: ${englishRatio}, is English: ${isEnglish}`);
  return isEnglish;
}

// 翻译函数
async function translateText(text, sourceLang, targetLang = 'EN') {
  if (sourceLang.toUpperCase() === targetLang.toUpperCase()) {
    return text;
  }

  if (!process.env.DEEPLX_API_URL) {
    console.warn('DEEPLX_API_URL not configured, skipping translation');
    return text;
  }

  try {
    console.log(`[DeepLX] Translating from ${sourceLang} to ${targetLang}: "${text.substring(0, 50)}..."`);

    const requestBody = {
      text,
      source_lang: sourceLang.toUpperCase(),
      target_lang: targetLang.toUpperCase()
    };

    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    };

    console.log(`[DeepLX] Using API URL: ${process.env.DEEPLX_API_URL}`);

    const response = await fetch(process.env.DEEPLX_API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(10000)
    });

    console.log(`[DeepLX] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DeepLX] HTTP Error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('[DeepLX] API Response:', result);
    
    if (result.code === 200 && result.data) {
      console.log(`[DeepLX] Translation successful: "${result.data.substring(0, 50)}..."`);
      return result.data;
    }
    
    console.error('[DeepLX] Translation failed:', result.message || 'Unknown error', result);
    return text;
  } catch (error) {
    console.error('[DeepLX] Translation error:', error);
    return text;
  }
}

// 主测试函数
async function testTranslation() {
  const testPrompt = "一个年轻女子在河边散步， 夕阳余晖, in the style of notion style, in the style of flat design,monochrome, just black and white";
  const userLocale = "zh";

  console.log("=== 修复后的翻译功能测试 ===\n");
  console.log(`用户语言: ${userLocale}`);
  console.log(`输入提示词: "${testPrompt}"`);
  console.log();

  // 1. 检查是否需要翻译
  const shouldTranslateToEnglish = (locale) => {
    const baseLocale = locale.toLowerCase().split('-')[0];
    return baseLocale !== 'en';
  };
  
  const needsTranslation = shouldTranslateToEnglish(userLocale) && !isTextPrimarylyEnglish(testPrompt);
  console.log(`是否需要翻译: ${needsTranslation}`);
  
  if (needsTranslation) {
    const sourceLanguage = 'ZH'; // 中文
    console.log(`源语言代码: ${sourceLanguage}`);
    
    try {
      console.log("开始翻译...");
      const translatedPrompt = await translateText(testPrompt, sourceLanguage, 'EN');
      
      console.log();
      console.log("=== 翻译结果 ===");
      console.log(`原文: "${testPrompt}"`);
      console.log(`译文: "${translatedPrompt}"`);
      console.log(`翻译成功: ${translatedPrompt !== testPrompt ? '✅' : '❌'}`);
      
    } catch (error) {
      console.error("翻译失败:", error);
    }
  } else {
    console.log("跳过翻译");
  }
}

testTranslation().catch(console.error);
