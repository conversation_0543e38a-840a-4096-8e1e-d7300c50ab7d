import { getDb } from '@/db';
import { generatedImage } from '@/db/schema';
import { and, count, desc, eq } from 'drizzle-orm';
import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 获取查询参数
    const page = Number.parseInt(searchParams.get('page') || '1');
    const limit = Number.parseInt(searchParams.get('limit') || '15');
    const isPublicParam = searchParams.get('isPublic');
    const isPublic = isPublicParam === 'true' ? true : isPublicParam === 'false' ? false : undefined;
    const userId = searchParams.get('userId');
    const modelName = searchParams.get('modelName');

    // 验证参数
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Invalid page or limit parameters' },
        { status: 400 }
      );
    }

    const db = await getDb();
    const offset = (page - 1) * limit;

    // 构建查询条件
    const conditions = [];

    // 只有明确传递 isPublic 参数时才添加公开/私有筛选条件
    if (isPublic !== undefined) {
      conditions.push(eq(generatedImage.isPublic, isPublic));
    }

    // 如果传递了 userId，筛选该用户的图片
    if (userId) {
      conditions.push(eq(generatedImage.userId, userId));
    }

    // 如果传递了 modelName，筛选该模型的图片
    if (modelName) {
      conditions.push(eq(generatedImage.modelName, modelName));
    }

    // 只显示状态为 1 的图片（成功生成的）
    conditions.push(eq(generatedImage.status, 1));



    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // 获取图片数据
    const images = await db
      .select({
        id: generatedImage.id,
        uuid: generatedImage.uuid,
        userId: generatedImage.userId,
        imageUrl: generatedImage.imageUrl,
        imageSize: generatedImage.imageSize,
        width: generatedImage.width,
        height: generatedImage.height,
        format: generatedImage.format,
        prompt: generatedImage.prompt,
        imageDescription: generatedImage.imageDescription,
        modelName: generatedImage.modelName,
        modelParams: generatedImage.modelParams,
        generationTime: generatedImage.generationTime,
        status: generatedImage.status,
        isPublic: generatedImage.isPublic,
        slug: generatedImage.slug,
        createdAt: generatedImage.createdAt,
        updatedAt: generatedImage.updatedAt,
      })
      .from(generatedImage)
      .where(whereClause)
      .orderBy(desc(generatedImage.createdAt))
      .limit(limit)
      .offset(offset);

    // 获取总数
    const totalResult = await db
      .select({ count: count() })
      .from(generatedImage)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // 获取可用的模型列表
    const modelsResult = await db
      .selectDistinct({ modelName: generatedImage.modelName })
      .from(generatedImage)
      .where(whereClause);

    const availableModels = modelsResult
      .map((m) => m.modelName)
      .filter(Boolean);

    return NextResponse.json({
      images,
      total,
      page,
      limit,
      totalPages,
      availableModels,
    });
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
