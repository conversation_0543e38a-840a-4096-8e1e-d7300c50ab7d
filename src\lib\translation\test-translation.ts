/**
 * 翻译功能测试工具
 * 
 * 用于测试DeepLX翻译服务是否正常工作
 */

import { translateText, isTextPrimarylyEnglish } from './deeplx';
import { getDeepLXLanguageCode, shouldTranslateToEnglish } from './language-mapping';

/**
 * 测试翻译功能
 */
export async function testTranslation() {
  console.log('🧪 Testing DeepLX Translation Service...');
  
  // 测试用例
  const testCases = [
    { text: '一个美丽的花园', locale: 'zh', expected: 'should translate' },
    { text: '美しい庭園', locale: 'ja', expected: 'should translate' },
    { text: 'A beautiful garden', locale: 'en', expected: 'should skip' },
    { text: 'Beautiful garden scene', locale: 'zh', expected: 'should skip (primarily English)' },
  ];
  
  for (const testCase of testCases) {
    console.log(`\n📝 Testing: "${testCase.text}" (${testCase.locale})`);
    
    // 检查是否需要翻译
    const needsTranslation = shouldTranslateToEnglish(testCase.locale);
    const isPrimarylyEnglish = isTextPrimarylyEnglish(testCase.text);
    const sourceLanguage = getDeepLXLanguageCode(testCase.locale);
    
    console.log(`   Locale: ${testCase.locale} -> DeepLX: ${sourceLanguage}`);
    console.log(`   Needs translation: ${needsTranslation}`);
    console.log(`   Is primarily English: ${isPrimarylyEnglish}`);
    
    if (needsTranslation && !isPrimarylyEnglish) {
      console.log(`   🔄 Translating...`);
      const translated = await translateText(testCase.text, sourceLanguage, 'EN');
      console.log(`   ✅ Result: "${translated}"`);
    } else {
      console.log(`   ⏭️  Skipping translation`);
    }
  }
  
  console.log('\n✅ Translation test completed!');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testTranslation().catch(console.error);
}
