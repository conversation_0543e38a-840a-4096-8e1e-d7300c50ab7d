/**
 * 模拟前端翻译逻辑的完整测试
 */

// 模拟前端翻译客户端
async function translateTextClient(text, locale) {
  try {
    const response = await fetch('http://localhost:3002/api/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text, locale }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('[Client] Translation error:', error);
    
    return {
      original: text,
      translated: text,
      wasTranslated: false,
      reason: `Translation failed: ${error.message}`
    };
  }
}

// 检查是否需要翻译
function shouldTranslateToEnglish(locale) {
  const baseLocale = locale.toLowerCase().split('-')[0];
  return baseLocale !== 'en';
}

// 检查文本是否主要是英文
function isTextPrimarylyEnglishClient(text) {
  const letters = text.replace(/[^\p{L}]/gu, '');
  if (letters.length === 0) return true;
  
  const englishLetters = letters.match(/[a-zA-Z]/g);
  const englishRatio = englishLetters ? englishLetters.length / letters.length : 0;
  
  const cjkPattern = /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/;
  const hasCJK = cjkPattern.test(text);
  
  if (hasCJK) {
    console.log(`[Translation] Text contains CJK characters, will translate despite English ratio: ${englishRatio}`);
    return false;
  }
  
  const isEnglish = englishRatio > 0.9;
  console.log(`[Translation] English ratio: ${englishRatio}, is English: ${isEnglish}`);
  return isEnglish;
}

// 模拟前端图片生成逻辑
async function simulateImageGeneration(userInput, userLocale, isColorful = false) {
  console.log('=== 前端翻译逻辑模拟 ===');
  console.log(`用户输入: "${userInput}"`);
  console.log(`用户语言: ${userLocale}`);
  console.log(`彩色模式: ${isColorful}`);
  console.log();

  const originalPrompt = userInput.trim();
  let translatedPrompt = originalPrompt;

  // 检查是否需要翻译
  const needsTranslation = shouldTranslateToEnglish(userLocale) && !isTextPrimarylyEnglishClient(originalPrompt);
  
  if (needsTranslation) {
    try {
      console.log(`🔄 需要翻译 - 从 ${userLocale.toUpperCase()} 到 EN`);
      const translationResult = await translateTextClient(originalPrompt, userLocale);
      
      if (translationResult.wasTranslated) {
        translatedPrompt = translationResult.translated;
        console.log(`✅ 翻译成功: "${originalPrompt}" -> "${translatedPrompt}"`);
      } else {
        console.log(`⚠️  未翻译: ${translationResult.reason}`);
      }
    } catch (error) {
      console.warn('❌ 翻译失败，使用原文:', error);
      translatedPrompt = originalPrompt;
    }
  } else {
    console.log(`⏭️  跳过翻译 - locale: ${userLocale}, isEnglish: ${isTextPrimarylyEnglishClient(originalPrompt)}`);
  }

  // 组合完整的生成提示词
  let generationPrompt = `${translatedPrompt}, in the style of notion style, in the style of flat design`;

  if (!isColorful) {
    generationPrompt = `${generationPrompt},monochrome, just black and white`;
  }

  console.log();
  console.log('=== 最终结果 ===');
  console.log(`原始输入 (存储用): "${originalPrompt}"`);
  console.log(`生成提示词: "${generationPrompt}"`);
  console.log();

  return {
    originalPrompt,
    generationPrompt,
    wasTranslated: translatedPrompt !== originalPrompt
  };
}

// 测试多种场景
async function runFullTests() {
  const testCases = [
    {
      input: "一只猫和一个女孩子在马路上",
      locale: "zh",
      isColorful: false,
      description: "中文输入，黑白模式"
    },
    {
      input: "A cat and a girl on the street", 
      locale: "en",
      isColorful: true,
      description: "英文输入，彩色模式"
    },
    {
      input: "川辺を散歩する若い女性、夕日",
      locale: "ja", 
      isColorful: false,
      description: "日文输入"
    },
    {
      input: "一只猫和一个女孩子在马路上, walking in the street",
      locale: "zh",
      isColorful: false,
      description: "中英混合输入"
    }
  ];

  for (const testCase of testCases) {
    console.log(`--- 测试 ${testCases.indexOf(testCase) + 1}: ${testCase.description} ---`);
    
    try {
      const result = await simulateImageGeneration(
        testCase.input, 
        testCase.locale, 
        testCase.isColorful
      );
      
      console.log('✅ 测试完成');
    } catch (error) {
      console.error('❌ 测试失败:', error);
    }
    
    console.log('='.repeat(80));
    console.log();
  }
}

runFullTests().catch(console.error);
