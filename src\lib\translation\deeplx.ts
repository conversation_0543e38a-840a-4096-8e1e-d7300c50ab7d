import { translateWithLibreTranslate, translateWithMyMemory } from './backup-translators';

/**
 * DeepLX Translation Service
 * 
 * 用于将用户输入的多语言提示词翻译为英语，以提高AI图片生成的效果
 * 文档：https://deeplx.owo.network/endpoints/official.html
 */

export interface TranslationRequest {
  text: string;
  source_lang: string;
  target_lang: string;
}

export interface TranslationResponse {
  code: number;
  data: string;
  message?: string;
}

/**
 * 将文本翻译为指定语言
 * @param text 要翻译的文本
 * @param sourceLang 源语言代码 (如: ZH, JA, AUTO)
 * @param targetLang 目标语言代码 (默认: EN)
 * @returns 翻译后的文本，如果翻译失败则返回原文
 */
export async function translateText(
  text: string,
  sourceLang: string,
  targetLang = 'EN'
): Promise<string> {
  // 如果源语言已经是目标语言，直接返回原文
  if (sourceLang.toUpperCase() === targetLang.toUpperCase()) {
    return text;
  }

  // 如果没有配置API URL，返回原文
  if (!process.env.DEEPLX_API_URL) {
    console.warn('DEEPLX_API_URL not configured, skipping translation');
    return text;
  }

  try {
    console.log(`[DeepLX] Translating from ${sourceLang} to ${targetLang}: "${text.substring(0, 50)}..."`);

    const requestBody: TranslationRequest = {
      text,
      source_lang: sourceLang.toUpperCase(),
      target_lang: targetLang.toUpperCase()
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    };

    // 新版本DeepLX API不需要额外的Authorization头，API Key已经包含在URL中
    console.log(`[DeepLX] Using API URL: ${process.env.DEEPLX_API_URL}`);

    const response = await fetch(process.env.DEEPLX_API_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
      // 设置较短的超时时间，避免影响用户体验
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    console.log(`[DeepLX] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DeepLX] HTTP Error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: TranslationResponse = await response.json();
    console.log('[DeepLX] API Response:', result);
    
    if (result.code === 200 && result.data) {
      console.log(`[DeepLX] Translation successful: "${result.data.substring(0, 50)}..."`);
      return result.data;
    }
    
    console.error('[DeepLX] Translation failed:', result.message || 'Unknown error', result);
    return text; // 翻译失败时返回原文
  } catch (error) {
    console.error('[DeepLX] Translation error:', error);
    
    // DeepLX 失败时，尝试备选翻译服务
    console.log('[DeepLX] Trying backup translation services...');
    
    try {
      // 首先尝试 LibreTranslate
      const backupResult = await translateWithLibreTranslate(text, sourceLang, targetLang);
      if (backupResult !== text) {
        console.log('[DeepLX] Backup translation successful with LibreTranslate');
        return backupResult;
      }
      
      // 如果 LibreTranslate 也失败，尝试 MyMemory
      const memoryResult = await translateWithMyMemory(text, sourceLang, targetLang);
      if (memoryResult !== text) {
        console.log('[DeepLX] Backup translation successful with MyMemory');
        return memoryResult;
      }
    } catch (backupError) {
      console.error('[DeepLX] All backup translation services failed:', backupError);
    }
    
    return text; // 所有翻译服务都失败时返回原文
  }
}

/**
 * 检查文本是否主要包含英文
 * 如果文本主要是英文，则跳过翻译
 */
export function isTextPrimarylyEnglish(text: string): boolean {
  // 移除标点符号、数字和空格，只保留字母
  const letters = text.replace(/[^\p{L}]/gu, '');
  if (letters.length === 0) return true; // 没有字母的情况默认为英文
  
  // 计算英文字母的比例
  const englishLetters = letters.match(/[a-zA-Z]/g);
  const englishRatio = englishLetters ? englishLetters.length / letters.length : 0;
  
  // 检查是否包含中文、日文、韩文等字符
  const cjkPattern = /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/;
  const hasCJK = cjkPattern.test(text);
  
  // 如果包含中日韩文字，即使英文比例高也需要翻译
  if (hasCJK) {
    console.log(`[Translation] Text contains CJK characters, will translate despite English ratio: ${englishRatio}`);
    return false;
  }
  
  // 如果英文字母占比超过90%，认为是英文文本
  const isEnglish = englishRatio > 0.9;
  console.log(`[Translation] English ratio: ${englishRatio}, is English: ${isEnglish}`);
  return isEnglish;
}
