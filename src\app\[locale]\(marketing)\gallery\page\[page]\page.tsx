import { GalleryClient } from '@/components/gallery/gallery-client';
import { HeaderSection } from '@/components/layout/header-section';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale; page: string }>;
}): Promise<Metadata> {
  const { locale, page } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const gt = await getTranslations({ locale, namespace: 'HomePage.gallery' });

  return constructMetadata({
    title: `${gt('title')} - Page ${page} | ${t('title')}`,
    description: gt('description'),
    canonicalUrl: getUrlWithLocale(`/gallery/page/${page}`, locale),
  });
}

interface GalleryPageProps {
  params: Promise<{ locale: Locale; page: string }>;
}

export default async function GalleryPageWithPagination({
  params,
}: GalleryPageProps) {
  const { locale, page } = await params;
  const t = await getTranslations({ locale, namespace: 'HomePage.gallery' });

  const currentPage = Number(page);

  // Validate page number
  if (Number.isNaN(currentPage) || currentPage < 1) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-background">
      <HeaderSection title={t('title')} description={t('subtitle')} />

      <GalleryClient currentPage={currentPage} />
    </div>
  );
}
