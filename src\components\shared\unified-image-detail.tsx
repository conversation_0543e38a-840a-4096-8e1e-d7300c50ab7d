'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Check,
  Clock,
  Copy,
  Download,
  FileText,
  Globe,
  Lock,
  Palette,
  Settings,
  Sparkles,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState } from 'react';

// Unified image data type
export interface UnifiedImageData {
  id: string;
  uuid?: string;
  userId?: string;
  imageUrl: string;
  imageSize?: string;
  width?: number;
  height?: number;
  format?: string;
  prompt: string;
  imageDescription?: string;
  modelName?: string;
  modelParams?: any;
  generationTime?: number;
  status?: number;
  isPublic?: boolean;
  slug?: string;
  createdAt: string;
  updatedAt?: string;
  // Compatible with different field names
  filename?: string;
  title?: string;
  originalDescription?: string;
}

interface UnifiedImageDetailProps {
  image: UnifiedImageData;
  showContainer?: boolean; // Whether to show container wrapper
  className?: string;
}

export function UnifiedImageDetail({
  image,
  showContainer = true,
  className = '',
}: UnifiedImageDetailProps) {
  const t = useTranslations('HomePage.imageDetail');
  const [copiedStates, setCopiedStates] = useState<{ [key: string]: boolean }>(
    {}
  );

  // Copy to clipboard
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates((prev) => ({ ...prev, [type]: true }));
      setTimeout(() => {
        setCopiedStates((prev) => ({ ...prev, [type]: false }));
      }, 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  // Download image
  const downloadImage = async (url: string) => {
    try {
      // Use API route proxy for download to avoid CORS issues
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: url }),
      });

      if (!response.ok) {
        throw new Error('Download failed');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      // Extract filename from URL, use default name if not available
      const urlParts = url.split('/');
      const filename =
        urlParts[urlParts.length - 1] ||
        `generated-image-${image.uuid || image.id}.png`;

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (err) {
      console.error('Failed to download: ', err);
      // If API route fails, try direct download (may have CORS issues)
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = `generated-image-${image.uuid || image.id}.png`;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackErr) {
        console.error('Fallback download also failed: ', fallbackErr);
      }
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // getMonth() returns 0-11, need +1
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  // Get Markdown format
  const getMarkdownUrl = () => {
    const title = image.title || image.prompt || 'Generated Image';
    return `![${title}](${image.imageUrl})`;
  };

  const content = (
    <div className={`grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto ${className}`}>
      {/* Image preview area */}
      <div className="lg:col-span-2 space-y-6">
        <div className="relative bg-muted rounded-lg overflow-hidden border flex items-center justify-center min-h-[400px]">
          <Image
            src={image.imageUrl || '/placeholder.svg'}
            alt={image.prompt}
            width={image.width || 1024}
            height={image.height || 1024}
            className="object-contain max-w-full max-h-full"
            sizes="(max-width: 1024px) 100vw, 66vw"
            priority
          />
          {/* Public/Private status indicator */}
          {image.isPublic !== undefined && (
            <div className="absolute top-3 right-3">
              <Badge
                variant={image.isPublic ? 'default' : 'secondary'}
                className="text-xs shadow-sm"
              >
                {image.isPublic ? (
                  <>
                    <Globe className="w-3 h-3 mr-1.5" />
                    {t('publicImage')}
                  </>
                ) : (
                  <>
                    <Lock className="w-3 h-3 mr-1.5" />
                    {t('privateImage')}
                  </>
                )}
              </Badge>
            </div>
          )}
        </div>
      </div>

      {/* 详情信息区域 */}
      <div className="space-y-6">
        {/* 操作按钮移动到右侧顶部 */}
        <div className="space-y-3">
          <h4 className="font-semibold text-foreground flex items-center gap-2">
            <Download className="w-4 h-4 text-muted-foreground" />
            {t('actions')}
          </h4>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              size="default"
              className="h-11 px-6"
              onClick={() => downloadImage(image.imageUrl)}
            >
              <Download className="w-4 h-4 mr-2" />
              {t('download')}
            </Button>
            <Button
              variant="outline"
              size="default"
              className="h-11 px-6"
              onClick={() => copyToClipboard(image.imageUrl, 'url')}
            >
              {copiedStates.url ? (
                <>
                  <Check className="w-4 h-4 mr-2 text-green-600" />
                  {t('copied')}
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 mr-2" />
                  {t('copyUrl')}
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="default"
              className="h-11 px-6"
              onClick={() => copyToClipboard(getMarkdownUrl(), 'md')}
            >
              {copiedStates.md ? (
                <>
                  <Check className="w-4 h-4 mr-2 text-green-600" />
                  {t('copied')}
                </>
              ) : (
                <>
                  <FileText className="w-4 h-4 mr-2" />
                  {t('copyMarkdown')}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 提示词 */}
        <div className="space-y-3">
          <h4 className="font-semibold text-foreground flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-muted-foreground" />
            {t('prompt')}
          </h4>
          <div className="bg-muted/50 p-4 rounded-lg border space-y-3">
            <p className="text-sm leading-relaxed text-foreground">
              {image.prompt}
            </p>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 -ml-1"
              onClick={() => copyToClipboard(image.prompt, 'prompt')}
            >
              {copiedStates.prompt ? (
                <>
                  <Check className="w-3 h-3 mr-1.5 text-green-600" />
                  {t('copied')}
                </>
              ) : (
                <>
                  <Copy className="w-3 h-3 mr-1.5" />
                  {t('prompt')}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 图片描述 */}
        {image.imageDescription && (
          <div className="space-y-3">
            <h4 className="font-semibold text-foreground flex items-center gap-2">
              <Palette className="w-4 h-4 text-muted-foreground" />
              {t('imageInfo')}
            </h4>
            <div className="p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
              <p className="text-sm text-foreground leading-relaxed">
                {image.imageDescription}
              </p>
            </div>
          </div>
        )}

        {/* 技术信息 */}
        <div className="space-y-3">
          <h4 className="font-semibold text-foreground flex items-center gap-2">
            <Settings className="w-4 h-4 text-muted-foreground" />
            {t('imageInfo')}
          </h4>
          <div className="space-y-3">
            <div className="grid grid-cols-1 gap-3">
              {image.generationTime && (
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                  <span className="text-sm text-muted-foreground">
                    {t('generationTime')}
                  </span>
                  <span className="text-sm text-foreground font-medium">
                    {image.generationTime}s
                  </span>
                </div>
              )}
              {image.width && image.height && (
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                  <span className="text-sm text-muted-foreground">
                    {t('dimensions')}
                  </span>
                  <span className="text-sm text-foreground font-medium">
                    {image.width} × {image.height}
                  </span>
                </div>
              )}
              {image.imageSize && (
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                  <span className="text-sm text-muted-foreground">
                    {t('fileSize')}
                  </span>
                  <span className="text-sm text-foreground font-medium">
                    {image.imageSize}
                  </span>
                </div>
              )}
              {image.format && (
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                  <span className="text-sm text-muted-foreground">
                    {t('format')}
                  </span>
                  <span className="text-sm text-foreground font-medium">
                    {image.format}
                  </span>
                </div>
              )}
            </div>
            {image.modelParams && (
              <div className="space-y-2">
                <span className="text-sm text-muted-foreground font-medium">
                  Model Parameters
                </span>
                <pre className="text-xs bg-muted/50 p-3 rounded-lg border overflow-x-auto font-mono text-foreground">
                  {JSON.stringify(image.modelParams, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* 创作时间 */}
        <div className="space-y-3">
          <h4 className="font-semibold text-foreground flex items-center gap-2">
            <Clock className="w-4 h-4 text-muted-foreground" />
            {t('createdAt')}
          </h4>
          <div className="p-4 bg-muted/50 rounded-lg border">
            <p className="text-sm text-foreground">
              {formatDate(image.createdAt)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  if (showContainer) {
    return <div className="container mx-auto px-4 py-8">{content}</div>;
  }

  return content;
}
