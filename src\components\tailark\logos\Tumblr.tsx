import * as React from 'react';
import { SVGProps } from 'react';

export default function Tumblr({
  width = 24,
  height = 24,
  fill = 'none',
  className,
  ...props
}: SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="-143 145 512 512"
      width={width}
      height={height}
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      aria-label="Tumblr logo"
      {...props}
    >
      <circle cx="113" cy="401" r="256" fill="#001935" />
      <path
        d="M185.2 515H185c-11 5.1-20.8 8.8-29.7 10.8-8.9 2.1-18.5 3.1-28.8 3.1-11.7 0-22.1-1.5-31-4.4-9-3-16.7-7.2-23-12.6-6.4-5.5-10.8-11.3-13.2-17.5-2.5-6.2-3.7-15.1-3.7-26.8v-89.8H27.3v-36.2c10.1-3.3 18.7-8 25.9-14.1 7.2-6.1 12.9-13.4 17.3-22 4.3-8.5 7.3-19.4 9-32.6h36.4v64.7h60.7v40.2h-60.7v65.6c0 14.9.8 24.4 2.4 28.6 1.6 4.2 4.5 7.6 8.8 10.1 5.6 3.4 12.1 5.1 19.4 5.1 13 0 25.8-4.2 38.7-12.6V515z"
        fill="#fff"
      />
    </svg>
  );
}
