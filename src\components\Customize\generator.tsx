'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { usePayment } from '@/hooks/use-payment';
import { cn } from '@/lib/utils';
import { translateTextClient, isTextPrimarylyEnglishClient } from '@/lib/translation/client';
import { shouldTranslateToEnglish } from '@/lib/translation/language-mapping';
import { ImageIcon, Lock, RefreshCw, Wand2 } from 'lucide-react';

import { useTranslations, useLocale } from 'next-intl';
import { useState } from 'react';

import { PromptSuggestions } from '../../ai/image/components/PromptSuggestions';
import { useImageGeneration } from '../../ai/image/hooks/use-image-generation';
import { imageHelpers } from '../../ai/image/lib/image-helpers';

import {
  MODEL_CONFIGS,
  type ModelMode,
  PROVIDERS,
  PROVIDER_ORDER,
  type ProviderKey,
  initializeProviderRecord,
} from '../../ai/image/lib/provider-config';
import type { Suggestion } from '../../ai/image/lib/suggestions';
import { getRandomSuggestions } from '../../ai/image/lib/suggestions';

// 简化的模型选择组件
function ModelOnlySelect({
  label,
  models,
  value,
  providerKey,
  onChange,
  enabled = true,
}: {
  label: string;
  models: string[];
  value: string;
  providerKey: ProviderKey;
  onChange: (value: string, providerKey: ProviderKey) => void;
  enabled?: boolean;
}) {
  return (
    <div className={cn('space-y-2', enabled ? '' : 'opacity-50')}>
      <Label>{label}</Label>
      <Select
        defaultValue={value}
        value={value}
        onValueChange={(selectedValue) => onChange(selectedValue, providerKey)}
      >
        <SelectTrigger className="cursor-pointer w-full">
          <SelectValue placeholder={value || 'Select a model'} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {models.map((model) => (
              <SelectItem key={model} value={model}>
                <span>
                  {imageHelpers.formatModelId(model).length > 40
                    ? imageHelpers.formatModelId(model).slice(0, 40) + '...'
                    : imageHelpers.formatModelId(model)}
                </span>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}

export function ImagePlayground({
  suggestions: initSuggestions,
}: {
  suggestions: Suggestion[];
}) {
  const t = useTranslations('HomePage.dashboard.generateImages.generator');
  const currentLocale = useLocale(); // 在组件顶层调用
  const {
    images,
    timings,
    failedProviders,
    isLoading,
    startGeneration,
    activePrompt,
  } = useImageGeneration();

  // 获取用户付费状态
  const { currentPlan, subscription } = usePayment();

  // 判断是否为付费用户（订阅用户或终身会员）
  const isPaidUser = currentPlan?.isLifetime || !!subscription;

  const [selectedModels, setSelectedModels] = useState<
    Record<ProviderKey, string>
  >(MODEL_CONFIGS.performance);
  const [enabledProviders, setEnabledProviders] = useState(
    initializeProviderRecord(true)
  );
  const [mode, setMode] = useState<ModelMode>('performance');
  const [prompt, setPrompt] = useState('');
  const [isPublic, setIsPublic] = useState(true); // 默认设置为公开
  const [isColorful, setIsColorful] = useState(false); // 默认黑白
  const [suggestions, setSuggestions] = useState<Suggestion[]>(initSuggestions);

  const updateSuggestions = () => {
    setSuggestions(getRandomSuggestions(5));
  };

  // Handle image click to navigate to detail page in new tab
  const handleImageClick = () => {
    if (replicateImage?.savedImage?.slug) {
      // If image is saved and has a slug, open detail page in new tab
      window.open(`/image/${replicateImage.savedImage.slug}`, '_blank');
    } else if (replicateImage?.image && !replicateFailed) {
      // If image is generated but not saved yet, show a message or handle accordingly
      console.log('Image generated but not saved yet');
    }
  };

  const handleModeChange = (newMode: ModelMode) => {
    setMode(newMode);
    setSelectedModels(MODEL_CONFIGS[newMode]);
  };

  const handleModelChange = (providerKey: ProviderKey, model: string) => {
    setSelectedModels((prev) => ({ ...prev, [providerKey]: model }));
  };

  const providerToModel = {
    replicate: selectedModels.replicate,
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    const originalPrompt = prompt.trim(); // 用户的原始输入
    let translatedPrompt = originalPrompt; // 默认使用原始输入

    // 检查是否需要翻译（非英语语言且包含非英语内容）
    const needsTranslation = shouldTranslateToEnglish(currentLocale) && !isTextPrimarylyEnglishClient(originalPrompt);
    
    if (needsTranslation) {
      try {
        console.log(`[Translation] Translating user input from ${currentLocale} to English`);
        const translationResult = await translateTextClient(originalPrompt, currentLocale);
        
        if (translationResult.wasTranslated) {
          translatedPrompt = translationResult.translated;
          console.log(`[Translation] Success: "${originalPrompt}" -> "${translatedPrompt}"`);
        } else {
          console.log(`[Translation] No translation performed: ${translationResult.reason}`);
        }
      } catch (error) {
        console.warn('[Translation] Failed, using original input:', error);
        translatedPrompt = originalPrompt; // 翻译失败时使用原文
      }
    } else {
      console.log(`[Translation] Skipped - locale: ${currentLocale}, isEnglish: ${isTextPrimarylyEnglishClient(originalPrompt)}`);
    }

    // 使用翻译后的内容组合完整的生成提示词
    let generationPrompt = `${translatedPrompt}, in the style of notion style, in the style of flat design`;

    // 如果选择黑白模式，添加黑白相关描述
    if (!isColorful) {
      generationPrompt = `${generationPrompt},monochrome, just black and white`;
    }

    const activeProviders = PROVIDER_ORDER.filter((p) => enabledProviders[p]);
    if (activeProviders.length > 0) {
      // 免费用户强制为公开，付费用户使用设置的值
      const finalIsPublic = isPaidUser ? isPublic : true;
      startGeneration(
        originalPrompt, // 保存原始用户输入到数据库
        generationPrompt, // 使用翻译后组合的提示词进行生成
        activeProviders,
        providerToModel,
        finalIsPublic
      );
    }
  };

  // 获取replicate的图片数据
  const replicateImage = images.find((img) => img.provider === 'replicate');
  const replicateTiming = timings.replicate;
  const replicateFailed = failedProviders.includes('replicate');

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-6 h-full">
          {/* 左侧控制面板 */}
          <div className="space-y-6">
            <Card>
              <CardContent className="p-6 space-y-6">
                <div>
                  <h1 className="text-2xl font-bold mb-2">{t('title')}</h1>
                  <p className="text-muted-foreground">{t('subtitle')}</p>
                </div>

                {/* AI 提示词输入框 */}
                <div className="space-y-3">
                  <Label htmlFor="ai-prompt">{t('aiPrompt')}</Label>
                  <Textarea
                    id="ai-prompt"
                    placeholder={t('promptPlaceholder')}
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[120px] resize-none"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleGenerate();
                      }
                    }}
                  />
                  {/* 提示用户可以使用任何语言输入 */}
                  <p className="text-sm text-muted-foreground">
                    💡 {t('multiLanguageTip')}
                  </p>
                </div>

                {/* 快捷提示词建议 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>{t('quickSuggestions')}</Label>
                    <button
                      type="button"
                      onClick={updateSuggestions}
                      disabled={isLoading}
                      className="flex items-center justify-center cursor-pointer px-2 rounded-lg py-1 bg-background text-sm hover:opacity-70 group transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <RefreshCw className="w-4 h-4 text-muted-foreground group-hover:opacity-70" />
                    </button>
                  </div>
                  <PromptSuggestions
                    suggestions={suggestions}
                    onSelect={(selectedPrompt) => setPrompt(selectedPrompt)}
                    disabled={isLoading}
                  />
                </div>

                <Separator />

                {/* 样式选择 */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="style-switch">
                        {t('styleSelection')}
                      </Label>
                      {!isPaidUser && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-800 rounded-md text-xs">
                          <Lock className="h-3 w-3" />
                          <span>Pro</span>
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {isPaidUser
                        ? t('styleDescription')
                        : t('styleDescriptionLocked')}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={cn(
                        'text-xs transition-colors',
                        !isColorful
                          ? 'text-foreground font-medium'
                          : 'text-muted-foreground',
                        !isPaidUser && 'opacity-50'
                      )}
                    >
                      {t('blackWhiteStyle')}
                    </span>
                    <Switch
                      id="style-switch"
                      checked={isColorful}
                      onCheckedChange={isPaidUser ? setIsColorful : undefined}
                      disabled={!isPaidUser}
                      className={cn(
                        !isPaidUser && 'opacity-50 cursor-not-allowed'
                      )}
                    />
                    <span
                      className={cn(
                        'text-xs transition-colors',
                        isColorful
                          ? 'text-foreground font-medium'
                          : 'text-muted-foreground',
                        !isPaidUser && 'opacity-50'
                      )}
                    >
                      {t('colorfulStyle')}
                    </span>
                  </div>
                </div>

                <Separator />

                {/* 模型选择已隐藏 - 直接使用 simple-illu 模型 */}

                {/* 公开设置 */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="public-switch">{t('publicWork')}</Label>
                      {!isPaidUser && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-amber-100 text-amber-800 rounded-md text-xs">
                          <Lock className="h-3 w-3" />
                          <span>Pro</span>
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {isPaidUser
                        ? t('publicWorkDescription')
                        : t('publicWorkDescriptionLocked')}
                    </p>
                  </div>
                  <Switch
                    id="public-switch"
                    checked={isPaidUser ? isPublic : true} // 免费用户强制为公开
                    onCheckedChange={isPaidUser ? setIsPublic : undefined}
                    disabled={!isPaidUser}
                    className={cn(
                      !isPaidUser && 'opacity-50 cursor-not-allowed'
                    )}
                  />
                </div>

                {/* 生成按钮 */}
                <Button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isLoading}
                  className="w-full h-12"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      {t('generating')}
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      {t('generateButton')}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* 右侧预览区域 */}
          <div className="space-y-6">
            <Card className="h-full">
              <CardContent className="p-6 h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">{t('preview')}</h2>
                </div>

                {/* 图片预览区域 */}
                <div className="flex-1 flex items-center justify-center bg-muted/50 rounded-lg border-2 border-dashed border-muted-foreground/30">
                  {replicateImage?.image && !replicateFailed ? (
                    <div
                      className={`relative w-80 h-80 group ${
                        replicateImage?.savedImage?.slug
                          ? 'cursor-pointer'
                          : 'cursor-default'
                      }`}
                      onClick={handleImageClick}
                    >
                      <img
                        src={`data:image/png;base64,${replicateImage.image}`}
                        alt="AI generated artwork"
                        className="w-full h-full object-cover rounded-lg transition-transform group-hover:scale-105"
                      />
                      {/* Hover hint */}
                      {replicateImage?.savedImage?.slug && (
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center rounded-lg">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 px-4 py-2 rounded-full text-sm font-medium">
                            {t('clickToViewDetailsWhenSaved')}
                          </div>
                        </div>
                      )}
                      {replicateImage?.image && !replicateImage?.savedImage && (
                        <div className="absolute top-2 right-2">
                          <div className="bg-blue-500/90 text-white px-2 py-1 rounded text-xs font-medium">
                            {t('imageSaving')}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground">
                      <ImageIcon className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg mb-2">{t('noImage')}</p>
                      <p className="text-sm">{t('noImageDescription')}</p>
                    </div>
                  )}
                </div>

                {/* 当前提示词和时间显示 */}
                {activePrompt && activePrompt.length > 0 && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground mb-1">
                      {t('currentPrompt')}:
                    </p>
                    <p className="text-sm">{activePrompt}</p>
                    {replicateTiming?.elapsed && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {t('generationTimeSeconds', {
                          seconds: (replicateTiming.elapsed / 1000).toFixed(1),
                        })}
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
