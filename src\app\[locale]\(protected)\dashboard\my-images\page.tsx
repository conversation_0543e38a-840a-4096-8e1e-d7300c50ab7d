'use client';

import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { ImageCard, type ImageCardData } from '@/components/shared/image-card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { authClient } from '@/lib/auth-client';
import { Routes } from '@/routes';
import { RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

// 图片数据类型
interface GeneratedImage {
  id: string;
  uuid: string;
  userId: string;
  imageUrl: string;
  imageSize: string;
  width: number;
  height: number;
  format: string;
  prompt: string;
  imageDescription?: string;
  modelName: string;
  modelParams?: any;
  generationTime?: number;
  status: number;
  isPublic: boolean;
  slug?: string;
  createdAt: string;
  updatedAt: string;
}

const IMAGES_PER_PAGE = 12;

/**
 * My Images page in Dashboard - 用户的图片管理页面
 */
export default function MyImagesPage() {
  const t = useTranslations('Common');
  const tMyImages = useTranslations('HomePage.dashboard.myImages');
  const { data: session } = authClient.useSession();
  const [images, setImages] = useState<GeneratedImage[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const breadcrumbs = [
    {
      label: tMyImages('breadcrumb'),
      isCurrentPage: true,
    },
  ];

  // 获取用户的图片数据
  const fetchMyImages = async (page: number) => {
    if (!session?.user?.id) {
      setError('User not logged in');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: IMAGES_PER_PAGE.toString(),
        userId: session.user.id, // 添加用户ID筛选
        // 不添加 isPublic 参数，这样会获取用户的所有图片（包括私有的）
      });

      const response = await fetch(`/api/images?${searchParams}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setImages(data.images || []);
      setTotal(data.total || 0);
      setTotalPages(data.totalPages || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取图片失败');
      console.error('Error fetching images:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载 - 等待 session 加载完成
  useEffect(() => {
    if (session?.user?.id) {
      fetchMyImages(1);
    }
  }, [session?.user?.id]);

  // 页面变化时重新加载
  useEffect(() => {
    if (currentPage > 1 && session?.user?.id) {
      fetchMyImages(currentPage);
    }
  }, [currentPage, session?.user?.id]);

  // 处理页面变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      <DashboardHeader breadcrumbs={breadcrumbs} />

      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-6 py-6 md:gap-8 md:py-8 px-4 lg:px-6 xl:px-8">
            {/* 页面标题 */}
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h1 className="text-2xl font-bold tracking-tight">
                  {tMyImages('title')}
                </h1>
                <p className="text-muted-foreground">{tMyImages('subtitle')}</p>
              </div>

              {!isLoading && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{tMyImages('totalImages', { count: total })}</span>
                  {totalPages > 1 && (
                    <span>
                      •{' '}
                      {tMyImages('pageInfo', {
                        current: currentPage,
                        total: totalPages,
                      })}
                    </span>
                  )}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => fetchMyImages(currentPage)}
                    title={tMyImages('refresh')}
                  >
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* 加载状态 */}
            {isLoading && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
                {[...Array(IMAGES_PER_PAGE)].map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <Skeleton className="aspect-[4/3] w-full" />
                    <CardContent className="p-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-full mb-2" />
                      <Skeleton className="h-3 w-2/3 mb-4" />
                      <div className="flex gap-2">
                        <Skeleton className="h-8 flex-1" />
                        <Skeleton className="h-8 w-16" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="text-center py-12">
                <p className="text-red-500 mb-4">{error}</p>
                <Button onClick={() => fetchMyImages(currentPage)}>
                  {tMyImages('retry')}
                </Button>
              </div>
            )}

            {/* 空状态 */}
            {!isLoading && !error && images.length === 0 && (
              <div className="text-center py-12">
                <p className="text-muted-foreground mb-4">
                  {tMyImages('noImages')}
                </p>
                <Button asChild>
                  <a href={Routes.DashboardGenerateImages}>
                    {tMyImages('startGenerating')}
                  </a>
                </Button>
              </div>
            )}

            {/* 图片网格 */}
            {!isLoading && !error && images.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
                {images.map((image) => {
                  const imageCardData: ImageCardData = {
                    id: image.id,
                    imageUrl: image.imageUrl,
                    prompt: image.prompt,
                    modelName: image.modelName,
                    isPublic: image.isPublic,
                    slug: image.slug,
                    createdAt: image.createdAt,
                    imageSize: image.imageSize,
                    format: image.format,
                    originalDescription: image.imageDescription,
                  };
                  return (
                    <ImageCard
                      key={image.id}
                      image={imageCardData}
                      showFilename={false}
                      showDetailedMetadata={true}
                    />
                  );
                })}
              </div>
            )}

            {/* 分页 */}
            {!isLoading && totalPages > 1 && (
              <div className="mt-8 flex items-center justify-center">
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    onClick={() =>
                      handlePageChange(Math.max(1, currentPage - 1))
                    }
                    disabled={currentPage === 1}
                    size="sm"
                  >
                    上一页
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum: number;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            currentPage === pageNum ? 'default' : 'outline'
                          }
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() =>
                      handlePageChange(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    size="sm"
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}

            {/* 页脚信息 */}
            {!isLoading && images.length > 0 && (
              <div className="text-center text-sm text-muted-foreground mt-8">
                显示第 {(currentPage - 1) * IMAGES_PER_PAGE + 1}-
                {Math.min(currentPage * IMAGES_PER_PAGE, total)} 张，共 {total}{' '}
                张图片
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
