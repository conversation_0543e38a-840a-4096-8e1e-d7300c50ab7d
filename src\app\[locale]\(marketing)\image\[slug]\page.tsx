import { HeaderSection } from '@/components/layout/header-section';
import { UnifiedImageDetail, type UnifiedImageData } from '@/components/shared/unified-image-detail';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import { notFound } from 'next/navigation';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getDb } from '@/db';
import { generatedImage } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getSession } from '@/lib/server';

interface ImageDetailPageProps {
  params: Promise<{
    locale: Locale;
    slug: string;
  }>;
}

// Image data type
interface ImageData {
  id: string;
  uuid: string;
  userId: string;
  imageUrl: string;
  imageSize?: string;
  width?: number;
  height?: number;
  format: string;
  prompt: string;
  imageDescription?: string;
  modelName: string;
  modelParams?: any;
  generationTime?: number;
  status: number;
  isPublic: boolean;
  slug?: string;
  createdAt: string;
  updatedAt: string;
}

// Get image data - direct database query, supports private image access
async function getImageData(slug: string): Promise<ImageData | null> {
  try {
    const db = await getDb();

    // Query image data
    const images = await db
      .select({
        id: generatedImage.id,
        uuid: generatedImage.uuid,
        userId: generatedImage.userId,
        imageUrl: generatedImage.imageUrl,
        imageSize: generatedImage.imageSize,
        width: generatedImage.width,
        height: generatedImage.height,
        format: generatedImage.format,
        prompt: generatedImage.prompt,
        imageDescription: generatedImage.imageDescription,
        modelName: generatedImage.modelName,
        modelParams: generatedImage.modelParams,
        generationTime: generatedImage.generationTime,
        status: generatedImage.status,
        isPublic: generatedImage.isPublic,
        slug: generatedImage.slug,
        createdAt: generatedImage.createdAt,
        updatedAt: generatedImage.updatedAt,
      })
      .from(generatedImage)
      .where(eq(generatedImage.slug, slug))
      .limit(1);

    if (!images || images.length === 0) {
      return null;
    }

    const imageData = images[0];

    // Check access permissions
    if (!imageData.isPublic) {
      // Get current user session
      const session = await getSession();

      // If image is not public, verify user permissions
      if (!session || session.user.id !== imageData.userId) {
        return null;
      }
    }

    // Convert data type to match ImageData interface
    return {
      ...imageData,
      imageSize: imageData.imageSize || undefined,
      width: imageData.width || undefined,
      height: imageData.height || undefined,
      imageDescription: imageData.imageDescription || undefined,
      modelParams: imageData.modelParams || undefined,
      generationTime: imageData.generationTime || undefined,
      slug: imageData.slug || undefined,
      createdAt: imageData.createdAt.toISOString(),
      updatedAt: imageData.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error('Error fetching image data:', error);
    return null;
  }
}

// Generate metadata
export async function generateMetadata({
  params,
}: ImageDetailPageProps): Promise<Metadata> {
  const { locale, slug } = await params;
  const image = await getImageData(slug);

  if (!image) {
    return constructMetadata({
      title: 'Image Not Found',
      description: 'The requested image does not exist or has been deleted',
    });
  }

  const title = image.prompt || 'Generated Image';
  const description = image.imageDescription || image.prompt;

  return constructMetadata({
    title: `${title}`,
    description: description.slice(0, 160),
    image: image.imageUrl,
    canonicalUrl: getUrlWithLocale(`/image/${slug}`, locale),
  });
}

export default async function ImageDetailPage({ params }: ImageDetailPageProps) {
  const { slug } = await params;
  const image = await getImageData(slug);

  if (!image) {
    notFound();
  }

  return (
    <>
      {/* <HeaderSection
        title={title}
        description={image.imageDescription || image.prompt}
      /> */}

      <UnifiedImageDetail image={image as UnifiedImageData} />
    </>
  );
}
