import Container from '@/components/layout/container';
import { BlurFadeDemo } from '@/components/magicui/example/blur-fade-example';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button, buttonVariants } from '@/components/ui/button';
import { websiteConfig } from '@/config/website';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import { cn } from '@/lib/utils';
import { MailIcon, TwitterIcon } from 'lucide-react';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const pt = await getTranslations({ locale, namespace: 'AboutPage' });

  return constructMetadata({
    title: pt('title') + ' | ' + t('title'),
    description: pt('description'),
    canonicalUrl: getUrlWithLocale('/about', locale),
  });
}

/**
 * inspired by https://astro-nomy.vercel.app/about
 */
export default async function AboutPage() {
  const t = await getTranslations('AboutPage');

  return (
    <Container className="py-16 px-4">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* about section */}
        <div className="relative max-w-(--breakpoint-md) mx-auto mb-24 mt-8 md:mt-16">
          <div className="mx-auto flex flex-col justify-between">
            <div className="grid gap-8 sm:grid-cols-2">
              {/* avatar and name */}
              <div className="flex items-center gap-8">
                <Avatar className="size-32 p-0.5">
                  <AvatarImage
                    className="rounded-full border-4 border-gray-200"
                    src="/logo.png"
                    alt="Avatar"
                  />
                  <AvatarFallback>
                    <div className="size-32 text-muted-foreground" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h1 className="text-4xl text-foreground">
                    {t('authorName')}
                  </h1>
                  <p className="text-base text-muted-foreground mt-2">
                    {t('authorBio')}
                  </p>
                </div>
              </div>

              {/* introduction */}
              <div>
                <p className="mb-8 text-base text-muted-foreground">
                  {t('introduction')}
                </p>

                <div className="flex items-center gap-4">
                  {websiteConfig.metadata.social?.twitter && (
                    <a
                      href={websiteConfig.metadata.social.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={cn(
                        buttonVariants({ variant: 'outline' }),
                        'rounded-lg cursor-pointer'
                      )}
                    >
                      <TwitterIcon className="mr-1 size-4" />
                      {t('followMe')}
                    </a>
                  )}
                  {websiteConfig.mail.supportEmail && (
                    <div className="flex items-center gap-4">
                      <Button className="rounded-lg cursor-pointer">
                        <MailIcon className="mr-1 size-4" />
                        <a href={`mailto:${websiteConfig.mail.supportEmail}`}>
                          {t('talkWithMe')}
                        </a>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Our Story Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold text-foreground mb-6">
            {t('ourStory.title')}
          </h2>
          <div className="prose prose-lg text-muted-foreground max-w-none">
            {t('ourStory.content').split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))}
          </div>
        </div>

        {/* Our Mission Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold text-foreground mb-6">
            {t('ourMission.title')}
          </h2>
          <div className="prose prose-lg text-muted-foreground max-w-none mb-8">
            {t('ourMission.content').split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))}
          </div>

          {/* Quote */}
          <blockquote className="border-l-4 border-primary pl-6 py-4 bg-muted/50 rounded-r-lg">
            <p className="text-lg italic text-foreground mb-2">
              "{t('ourMission.quote')}"
            </p>
            <cite className="text-sm text-muted-foreground">
              {t('ourMission.quoteAuthor')}
            </cite>
          </blockquote>
        </div>

        {/* Our Technology Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold text-foreground mb-6">
            {t('ourTechnology.title')}
          </h2>
          <div className="prose prose-lg text-muted-foreground max-w-none">
            {t('ourTechnology.content').split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))}
          </div>
        </div>

        {/* Our Values Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold text-foreground mb-6">
            {t('ourValues.title')}
          </h2>
          <div className="grid gap-8 md:grid-cols-2">
            <div className="bg-muted/30 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-foreground mb-3">
                {t('ourValues.simplicity.title')}
              </h3>
              <p className="text-muted-foreground">
                {t('ourValues.simplicity.content')}
              </p>
            </div>
            <div className="bg-muted/30 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-foreground mb-3">
                {t('ourValues.accessibility.title')}
              </h3>
              <p className="text-muted-foreground">
                {t('ourValues.accessibility.content')}
              </p>
            </div>
          </div>
        </div>

        {/* Join Our Journey Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold text-foreground mb-6">
            {t('joinOurJourney.title')}
          </h2>
          <div className="prose prose-lg text-muted-foreground max-w-none">
            {t('joinOurJourney.content').split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-4">
                {paragraph}
              </p>
            ))}
          </div>
        </div>

        {/* image section */}
      </div>
    </Container>
  );
}
