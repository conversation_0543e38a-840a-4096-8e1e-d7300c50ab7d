import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    // Verify URL is from allowed domains (security consideration)
    const allowedDomains = [
      'pub-f4ac0a2413d446fa94195952a095fe39.r2.dev', // Cloudflare R2
      'localhost',
      '127.0.0.1',
    ];

    let isAllowedDomain = false;
    try {
      const url = new URL(imageUrl);
      isAllowedDomain = allowedDomains.some(domain =>
        url.hostname === domain || url.hostname.endsWith(`.${domain}`)
      );
    } catch (err) {
      return NextResponse.json(
        { error: 'Invalid URL' },
        { status: 400 }
      );
    }

    if (!isAllowedDomain) {
      return NextResponse.json(
        { error: 'Domain not allowed' },
        { status: 403 }
      );
    }

    // Fetch image
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'AI-Illustration-Generator/1.0',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch image' },
        { status: response.status }
      );
    }

    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/png';

    // Return image data
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': 'attachment',
        'Cache-Control': 'public, max-age=31536000', // Cache for one year
      },
    });
  } catch (error) {
    console.error('Download image error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
