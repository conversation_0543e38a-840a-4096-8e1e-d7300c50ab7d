'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TestResult {
  original: string;
  locale: string;
  sourceLanguage: string;
  needsTranslation: boolean;
  isPrimarylyEnglish: boolean;
  translated: string;
  translationStatus: string;
  translationTime: number;
}

export default function TranslationTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [customText, setCustomText] = useState('');
  const [customLocale, setCustomLocale] = useState('zh');

  const runAutoTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-translation');
      const data = await response.json();
      
      if (data.success) {
        setTestResults(data.results);
      } else {
        console.error('Test failed:', data.error);
      }
    } catch (error) {
      console.error('Test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testCustomText = async () => {
    if (!customText.trim()) return;
    
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-translation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: customText, locale: customLocale }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setTestResults([data.result]);
      } else {
        console.error('Custom test failed:', data.error);
      }
    } catch (error) {
      console.error('Custom test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-500';
      case 'failed': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      case 'skipped': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success': return '翻译成功';
      case 'failed': return '翻译失败';
      case 'error': return '翻译错误';
      case 'skipped': return '跳过翻译';
      default: return status;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">翻译功能测试</h1>
          <p className="text-gray-600 mt-2">测试DeepLX自动翻译功能是否正常工作</p>
        </div>

        {/* 自动测试 */}
        <Card>
          <CardHeader>
            <CardTitle>自动测试</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={runAutoTest} disabled={isLoading} className="w-full">
              {isLoading ? '测试中...' : '运行自动测试'}
            </Button>
          </CardContent>
        </Card>

        {/* 自定义测试 */}
        <Card>
          <CardHeader>
            <CardTitle>自定义测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">选择语言</label>
              <Select value={customLocale} onValueChange={setCustomLocale}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh">中文</SelectItem>
                  <SelectItem value="ja">日语</SelectItem>
                  <SelectItem value="ko">韩语</SelectItem>
                  <SelectItem value="fr">法语</SelectItem>
                  <SelectItem value="de">德语</SelectItem>
                  <SelectItem value="en">英语</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">输入测试文本</label>
              <Textarea
                value={customText}
                onChange={(e) => setCustomText(e.target.value)}
                placeholder="输入要测试的文本..."
                className="min-h-[100px]"
              />
            </div>
            <Button onClick={testCustomText} disabled={isLoading || !customText.trim()} className="w-full">
              {isLoading ? '测试中...' : '测试翻译'}
            </Button>
          </CardContent>
        </Card>

        {/* 测试结果 */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>测试结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">测试 {index + 1}</span>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(result.translationStatus)}>
                          {getStatusText(result.translationStatus)}
                        </Badge>
                        {result.translationTime > 0 && (
                          <Badge variant="outline">{result.translationTime}ms</Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">原文 ({result.locale}):</span>
                        <p className="mt-1 p-2 bg-gray-50 rounded">{result.original}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">翻译结果:</span>
                        <p className="mt-1 p-2 bg-blue-50 rounded">{result.translated}</p>
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-500 space-y-1">
                      <p>源语言代码: {result.sourceLanguage}</p>
                      <p>需要翻译: {result.needsTranslation ? '是' : '否'}</p>
                      <p>主要是英文: {result.isPrimarylyEnglish ? '是' : '否'}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
