import { translateText } from '@/lib/translation/deeplx';
import { getDeepLXLanguageCode, shouldTranslateToEnglish } from '@/lib/translation/language-mapping';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { text, locale } = await req.json();

    if (!text || !locale) {
      return NextResponse.json(
        { error: 'Missing required parameters: text and locale' },
        { status: 400 }
      );
    }

    // 检查是否需要翻译
    const needsTranslation = shouldTranslateToEnglish(locale);
    
    if (!needsTranslation) {
      return NextResponse.json({
        original: text,
        translated: text,
        wasTranslated: false,
        reason: 'Text is already in English or locale is English'
      });
    }

    // 获取源语言代码
    const sourceLanguage = getDeepLXLanguageCode(locale);
    
    // 执行翻译
    const translatedText = await translateText(text, sourceLanguage, 'EN');
    
    return NextResponse.json({
      original: text,
      translated: translatedText,
      wasTranslated: translatedText !== text,
      sourceLanguage,
      targetLanguage: 'EN'
    });

  } catch (error) {
    console.error('[API] Translation error:', error);
    return NextResponse.json(
      { 
        error: 'Translation failed', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
