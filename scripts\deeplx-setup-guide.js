/**
 * DeepLX API Key 获取指南
 * 
 * 由于你只有 Client ID 和 Client Secret，需要完成 OAuth 认证流程
 * 才能获取真正的 DeepLX API Key
 */

console.log('🔑 DeepLX API Key 获取指南\n');

console.log('❌ 问题：直接使用 Client ID/Secret 作为 API Key 无效');
console.log('   Client ID 和 Client Secret 是用于 OAuth 认证的，不是 API Key\n');

console.log('✅ 解决方案：完成 LINUX DO Connect OAuth 认证流程\n');

console.log('📋 获取步骤：');
console.log('1. 访问：https://connect.linux.do');
console.log('2. 选择合适的应用程序进行 OAuth 认证');
console.log('3. 使用你的认证信息：');
console.log('   - Client ID: dAO1FDMtDsS2xiSzVKI7ygOlYvCrtt2g');
console.log('   - Client Secret: S5eTeNYXeoQNwQbK1M5NcL8i3WrjcrZo');
console.log('4. 完成认证后，系统会给你一个真正的 DeepLX API Key');
console.log('5. 这个 API Key 才是你需要在 .env 文件中使用的\n');

console.log('⚠️  注意：');
console.log('- Client ID/Secret ≠ API Key');
console.log('- 必须完成完整的 OAuth 认证流程');
console.log('- API Key 通常是一个较长的随机字符串\n');

console.log('🔧 临时替代方案：');
console.log('如果无法获取 DeepLX API Key，可以考虑：');
console.log('1. 使用其他翻译服务（如 Google Translate API）');
console.log('2. 或者暂时禁用翻译功能，让用户直接使用英语输入');
console.log('3. 或者使用免费的翻译服务作为备选\n');

console.log('💡 建议：');
console.log('先访问 https://connect.linux.do 完成认证，获取真正的 API Key');
