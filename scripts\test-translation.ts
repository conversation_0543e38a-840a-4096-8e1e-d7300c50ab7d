/**
 * 简单的翻译功能验证脚本
 * 运行: npm run dev 后在另一个终端运行: npx tsx scripts/test-translation.ts
 */

import { translateText, isTextPrimarylyEnglish } from '../src/lib/translation/deeplx';
import { getDeepLXLanguageCode, shouldTranslateToEnglish } from '../src/lib/translation/language-mapping';

async function testTranslationSimple() {
  console.log('🧪 测试DeepLX翻译功能...\n');
  
  // 测试用例
  const testCases = [
    { text: '一只可爱的小猫', locale: 'zh', description: '中文测试' },
    { text: '美しい桜の花', locale: 'ja', description: '日语测试' },
    { text: 'A beautiful cat', locale: 'en', description: '英语测试（应跳过）' },
    { text: 'cute cat in garden', locale: 'zh', description: '英文内容测试（应跳过）' },
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📝 测试 ${i + 1}: ${testCase.description}`);
    console.log(`   输入: "${testCase.text}"`);
    console.log(`   语言: ${testCase.locale}`);
    
    try {
      // 检查是否需要翻译
      const needsTranslation = shouldTranslateToEnglish(testCase.locale);
      const isPrimarylyEnglish = isTextPrimarylyEnglish(testCase.text);
      const sourceLanguage = getDeepLXLanguageCode(testCase.locale);
      
      console.log(`   需要翻译: ${needsTranslation}`);
      console.log(`   主要是英文: ${isPrimarylyEnglish}`);
      console.log(`   源语言代码: ${sourceLanguage}`);
      
      if (needsTranslation && !isPrimarylyEnglish) {
        console.log(`   🔄 正在翻译...`);
        const startTime = Date.now();
        const translated = await translateText(testCase.text, sourceLanguage, 'EN');
        const endTime = Date.now();
        
        if (translated !== testCase.text) {
          console.log(`   ✅ 翻译成功 (${endTime - startTime}ms): "${translated}"`);
        } else {
          console.log(`   ⚠️  翻译可能失败，返回原文`);
        }
      } else {
        console.log(`   ⏭️  跳过翻译`);
      }
    } catch (error) {
      console.log(`   ❌ 错误:`, error instanceof Error ? error.message : error);
    }
    
    console.log(''); // 空行分隔
  }
  
  console.log('✅ 测试完成！');
}

// 测试环境变量
function checkEnvironment() {
  console.log('🔧 检查环境变量...');
  console.log(`DEEPLX_API_URL: ${process.env.DEEPLX_API_URL || '❌ 未设置'}`);
  console.log(`DEEPLX_API_KEY: ${process.env.DEEPLX_API_KEY ? '✅ 已设置' : '⚠️  未设置（可选）'}`);
  console.log('');
}

// 主函数
async function main() {
  checkEnvironment();
  await testTranslationSimple();
}

main().catch(console.error);
