# 翻译功能集成说明

本功能为图片生成系统集成了自动翻译功能，使用DeepLX API将用户的多语言输入自动翻译为英语，以提高AI图片生成的效果。

## 🌟 功能特点

- **对用户透明**：翻译过程在后台自动进行，用户无感知
- **智能判断**：自动检测文本语言，如果已经是英语则跳过翻译
- **错误处理**：翻译失败时自动降级使用原文，不影响用户体验
- **多语言支持**：支持中文、日语、韩语等30+种语言
- **性能优化**：设置5秒超时，避免影响图片生成速度

## 📁 文件结构

```
src/lib/translation/
├── deeplx.ts              # DeepLX翻译服务核心逻辑
├── language-mapping.ts    # 语言代码映射工具
├── test-translation.ts    # 翻译功能测试工具
└── README.md             # 说明文档
```

## 🔧 配置

### 环境变量

在 `.env` 文件中添加以下配置：

```properties
# DeepLX Translation API (新版本 - LINUX DO Connect 认证)
# 获取API Key: https://connect.linux.do
DEEPLX_API_KEY="your-deeplx-api-key-here"
DEEPLX_API_URL="https://api.deeplx.org/your-deeplx-api-key-here/translate"
```

**获取API Key的步骤**：

1. **访问认证页面**: https://connect.linux.do
2. **使用LINUX DO Connect认证**:
   - Client ID: `dAO1FDMtDsS2xiSzVKI7ygOlYvCrtt2g`
   - Client Secret: `S5eTeNYXeoQNwQbK1M5NcL8i3WrjcrZo`
3. **获取DeepLX Api Key**: 认证成功后会显示你的专属API Key
4. **更新环境变量**: 将获取的API Key替换到 `.env` 文件中

**重要说明**：
- 新版本DeepLX需要LINUX DO Connect认证以防止滥用
- 每个用户有独立的API Key和URL
- URL格式: `https://api.deeplx.org/<your-api-key>/translate`
- 测试前请确保已获取有效的API Key

### 支持的语言

系统支持以下语言的自动翻译：

- 🇨🇳 中文 (zh)
- 🇯🇵 日语 (ja) 
- 🇰🇷 韩语 (ko)
- 🇫🇷 法语 (fr)
- 🇩🇪 德语 (de)
- 🇪🇸 西班牙语 (es)
- 🇮🇹 意大利语 (it)
- 🇵🇹 葡萄牙语 (pt)
- 🇷🇺 俄语 (ru)
- 以及其他20+种语言

## 🚀 工作流程

1. **用户输入**：用户使用任何语言输入提示词
2. **语言检测**：系统检测当前用户的界面语言
3. **智能判断**：判断是否需要翻译（非英语且文本不是主要英文）
4. **自动翻译**：调用DeepLX API将文本翻译为英语
5. **图片生成**：使用翻译后的英语提示词生成图片
6. **数据保存**：数据库中保存用户的原始输入

## 🧪 测试与验证

### 方法1: 快速API验证

```bash
# 1. 首先确保已获取有效的API Key
# 2. 运行快速测试脚本
node scripts/quick-translation-test.js
```

如果看到 `401 Unauthorized` 错误，说明需要重新获取有效的API Key。

### 方法2: 运行测试脚本

```bash
# 启动开发服务器
npm run dev

# 在另一个终端运行测试脚本
npx tsx scripts/test-translation.ts
```

### 方法2: 使用API测试端点

访问测试API端点查看翻译功能状态：

```bash
# 自动测试
curl http://localhost:3002/api/test-translation

# 自定义测试
curl -X POST http://localhost:3002/api/test-translation \
  -H "Content-Type: application/json" \
  -d '{"text":"一只可爱的小猫","locale":"zh"}'
```

### 方法3: 使用网页测试界面

访问测试页面进行可视化测试：
```
http://localhost:3002/dashboard/test-translation
```

### 方法4: 实际使用验证

1. 切换界面语言为中文 (zh)
2. 访问图片生成页面: `http://localhost:3002/dashboard/generate-images`
3. 输入中文提示词，如：`一只可爱的小猫在花园里玩耍`
4. 查看服务器控制台日志，应该看到类似输出：

```
[abc123] User locale: zh
[abc123] Auto-translating from ZH to EN
[DeepLX] Translating from ZH to EN: "一只可爱的小猫在花园里玩耍..."
[DeepLX] Translation successful: "A cute little cat playing in the garden..."
[abc123] Translation successful: "一只可爱的小猫在花园里玩耍" -> "A cute little cat playing in the garden"
[abc123] Starting image generation [requestId=abc123, provider=replicate, model=yiquan00/simple-illu, prompt="simple-illu A cute little cat playing in the garden" (translated from zh)]
```

### 验证检查清单

- [ ] 环境变量 `DEEPLX_API_URL` 已配置
- [ ] 环境变量 `DEEPLX_API_KEY` 已配置（可选）
- [ ] 测试脚本运行成功
- [ ] API测试端点返回正确结果
- [ ] 网页测试界面显示翻译结果
- [ ] 实际图片生成时控制台显示翻译日志
- [ ] 非英语输入被正确翻译为英语
- [ ] 英语输入被正确跳过翻译

## 📝 日志监控

### 开发环境日志

系统会记录详细的翻译日志，启动开发服务器后查看控制台：

```bash
npm run dev
```

翻译成功时的日志示例：
```
[abc123] User locale: zh
[abc123] Auto-translating from ZH to EN
[DeepLX] Translating from ZH to EN: "美丽的花园..."
[DeepLX] Translation successful: "Beautiful garden..."
[abc123] Translation successful: "美丽的花园" -> "Beautiful garden"
[abc123] Starting image generation [requestId=abc123, provider=replicate, model=yiquan00/simple-illu, prompt="simple-illu Beautiful garden" (translated from zh)]
```

翻译跳过时的日志示例：
```
[abc123] User locale: en
[abc123] Translation skipped - already English or English locale
[abc123] Starting image generation [requestId=abc123, provider=replicate, model=yiquan00/simple-illu, prompt="simple-illu Beautiful garden"]
```

翻译失败时的日志示例：
```
[abc123] User locale: zh
[abc123] Auto-translating from ZH to EN
[DeepLX] Translation error: HTTP 429: Too Many Requests
[abc123] Translation failed, using original prompt: Error: HTTP 429: Too Many Requests
[abc123] Starting image generation [requestId=abc123, provider=replicate, model=yiquan00/simple-illu, prompt="simple-illu 美丽的花园"]
```

### 生产环境监控

在生产环境中，建议监控以下指标：

1. **翻译成功率**：翻译成功的请求占总翻译请求的比例
2. **翻译延迟**：翻译API的响应时间
3. **翻译错误**：翻译失败的错误类型和频率
4. **API使用量**：DeepLX API的调用次数和配额使用情况

## ⚠️ 注意事项

1. **API限制**：DeepLX API可能有使用限制，建议配置API密钥
2. **超时处理**：翻译请求设置5秒超时，避免影响用户体验
3. **降级策略**：翻译失败时自动使用原文，确保功能可用
4. **隐私保护**：翻译仅用于优化生成效果，不存储翻译内容

## 🔧 故障排除

### 翻译不工作

1. 检查 `DEEPLX_API_URL` 是否正确配置
2. 检查网络连接是否正常
3. 查看服务器日志中的错误信息

### 生成效果不佳

1. 检查翻译结果是否准确
2. 考虑手动使用英语输入进行对比
3. 检查模型是否正确处理翻译后的提示词

## 📈 未来扩展

- 支持更多翻译服务提供商
- 添加翻译缓存机制
- 支持用户自定义翻译偏好
- 添加翻译质量评估
