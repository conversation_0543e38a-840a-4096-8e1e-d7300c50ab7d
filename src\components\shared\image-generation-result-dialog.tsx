'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ImageDetailView, type ImageDetailData } from './image-detail-view';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface ImageGenerationResultDialogProps {
  isOpen: boolean;
  onClose: () => void;
  image: ImageDetailData;
  title?: string;
}

export function ImageGenerationResultDialog({
  isOpen,
  onClose,
  image,
  title = '图片生成完成',
}: ImageGenerationResultDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              {title}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(90vh-8rem)]">
          <ImageDetailView
            image={image}
            showCreator={false}
            showModelInfo={false} // 也隐藏生成结果中的模型信息
            showPrivacyStatus={false}
            showExternalLink={false}
            className="max-w-none"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
