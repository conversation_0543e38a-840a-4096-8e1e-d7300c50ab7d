import { getDb } from '@/db';
import { generatedImage } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { type NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json(
        { error: 'Slug parameter is required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // 获取图片数据
    const image = await db
      .select({
        id: generatedImage.id,
        uuid: generatedImage.uuid,
        userId: generatedImage.userId,
        imageUrl: generatedImage.imageUrl,
        imageSize: generatedImage.imageSize,
        width: generatedImage.width,
        height: generatedImage.height,
        format: generatedImage.format,
        prompt: generatedImage.prompt,
        imageDescription: generatedImage.imageDescription,
        modelName: generatedImage.modelName,
        modelParams: generatedImage.modelParams,
        generationTime: generatedImage.generationTime,
        status: generatedImage.status,
        isPublic: generatedImage.isPublic,
        slug: generatedImage.slug,
        createdAt: generatedImage.createdAt,
        updatedAt: generatedImage.updatedAt,
      })
      .from(generatedImage)
      .where(eq(generatedImage.slug, slug))
      .limit(1);

    if (!image || image.length === 0) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    const imageData = image[0];

    // 检查图片访问权限
    if (!imageData.isPublic) {
      // 获取当前用户 session
      const session = await getSession();

      // 如果图片不是公开的，需要验证用户权限
      if (!session || session.user.id !== imageData.userId) {
        return NextResponse.json(
          { error: 'Image not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      image: imageData,
    });
  } catch (error) {
    console.error('Error fetching image:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
