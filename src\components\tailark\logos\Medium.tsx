import { SVGProps } from 'react';

export default function Medium(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...props}>
      <title>{'Medium'}</title>
      <circle cx="12" cy="12" r="12" fill="#12100E" />
      <g fill="#fff">
        <ellipse cx="7.5" cy="12" rx="2.5" ry="5" />
        <ellipse cx="12" cy="12" rx="2" ry="5" />
        <ellipse cx="16.5" cy="12" rx="1.5" ry="5" />
      </g>
    </svg>
  );
}
