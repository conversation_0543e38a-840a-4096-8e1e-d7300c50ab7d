/**
 * 测试备选翻译服务
 * 运行: node scripts/test-backup-translation.js
 */

// 模拟备选翻译服务的实现
async function translateWithLibreTranslate(text, sourceLang, targetLang = 'en') {
  const langMapping = {
    'ZH': 'zh', 'JA': 'ja', 'EN': 'en', 'KO': 'ko',
    'FR': 'fr', 'DE': 'de', 'ES': 'es', 'IT': 'it',
    'PT': 'pt', 'RU': 'ru',
  };

  const sourceCode = langMapping[sourceLang.toUpperCase()] || sourceLang.toLowerCase();
  const targetCode = langMapping[targetLang.toUpperCase()] || targetLang.toLowerCase();

  if (sourceCode === targetCode) {
    return text;
  }

  try {
    console.log(`[LibreTranslate] Translating from ${sourceCode} to ${targetCode}: "${text.substring(0, 50)}..."`);

    const requestBody = {
      q: text,
      source: sourceCode,
      target: targetCode,
      format: 'text'
    };

    const response = await fetch('https://libretranslate.com/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[LibreTranslate] HTTP Error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.translatedText) {
      console.log(`[LibreTranslate] Translation successful: "${result.translatedText.substring(0, 50)}..."`);
      return result.translatedText;
    }
    
    console.error('[LibreTranslate] No translation result');
    return text;
  } catch (error) {
    console.error('[LibreTranslate] Translation error:', error.message);
    return text;
  }
}

async function translateWithMyMemory(text, sourceLang, targetLang = 'en') {
  const sourceCode = sourceLang.toLowerCase();
  const targetCode = targetLang.toLowerCase();

  if (sourceCode === targetCode) {
    return text;
  }

  try {
    console.log(`[MyMemory] Translating from ${sourceCode} to ${targetCode}: "${text.substring(0, 50)}..."`);

    const encodedText = encodeURIComponent(text);
    const url = `https://api.mymemory.translated.net/get?q=${encodedText}&langpair=${sourceCode}|${targetCode}`;

    const response = await fetch(url, {
      method: 'GET',
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.responseData?.translatedText) {
      const translatedText = result.responseData.translatedText;
      console.log(`[MyMemory] Translation successful: "${translatedText.substring(0, 50)}..."`);
      return translatedText;
    }
    
    console.error('[MyMemory] No translation result');
    return text;
  } catch (error) {
    console.error('[MyMemory] Translation error:', error.message);
    return text;
  }
}

async function testBackupTranslation() {
  console.log('🧪 Testing backup translation services...\n');
  
  const testCases = [
    { text: '一只可爱的小猫', sourceLang: 'zh', description: '中文测试' },
    { text: '美しい桜の花', sourceLang: 'ja', description: '日语测试' },
  ];
  
  for (const testCase of testCases) {
    console.log(`📝 Testing: ${testCase.description}`);
    console.log(`   Input: "${testCase.text}"`);
    
    // 测试 LibreTranslate
    console.log('\n   🔄 Testing LibreTranslate...');
    const libreResult = await translateWithLibreTranslate(testCase.text, testCase.sourceLang, 'en');
    console.log(`   Result: "${libreResult}"`);
    
    // 测试 MyMemory
    console.log('\n   🔄 Testing MyMemory...');
    const memoryResult = await translateWithMyMemory(testCase.text, testCase.sourceLang, 'en');
    console.log(`   Result: "${memoryResult}"`);
    
    console.log('\n' + '='.repeat(50) + '\n');
  }
  
  console.log('✅ Backup translation test completed!');
}

testBackupTranslation();
