import { SVGProps } from 'react';

export default function Blogger(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <title>{'Blogger'}</title>
      <circle cx="50" cy="50" r="50" fill="#FF8000" />
      <rect x="28" y="35" width="44" height="10" rx="5" fill="#fff" />
      <rect x="28" y="55" width="32" height="10" rx="5" fill="#fff" />
      <circle cx="70" cy="60" r="5" fill="#fff" />
    </svg>
  );
}