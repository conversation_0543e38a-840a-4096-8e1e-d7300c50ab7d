/**
 * 测试翻译检测逻辑
 */

// 模拟 isTextPrimarylyEnglish 函数
function isTextPrimarylyEnglish(text) {
  // 移除标点符号、数字和空格，只保留字母
  const letters = text.replace(/[^\p{L}]/gu, '');
  if (letters.length === 0) return true; // 没有字母的情况默认为英文
  
  // 计算英文字母的比例
  const englishLetters = letters.match(/[a-zA-Z]/g);
  const englishRatio = englishLetters ? englishLetters.length / letters.length : 0;
  
  // 检查是否包含中文、日文、韩文等字符
  const cjkPattern = /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/;
  const hasCJK = cjkPattern.test(text);
  
  // 如果包含中日韩文字，即使英文比例高也需要翻译
  if (hasCJK) {
    console.log(`[Translation] Text contains CJK characters, will translate despite English ratio: ${englishRatio}`);
    return false;
  }
  
  // 如果英文字母占比超过90%，认为是英文文本
  const isEnglish = englishRatio > 0.9;
  console.log(`[Translation] English ratio: ${englishRatio}, is English: ${isEnglish}`);
  return isEnglish;
}

// 测试用例
const testCases = [
  {
    text: "一个年轻女子在河边散步， 夕阳余晖, in the style of notion style, in the style of flat design,monochrome, just black and white",
    expected: false, // 应该需要翻译，因为包含中文
    description: "中英混合文本"
  },
  {
    text: "一个年轻女子在河边散步， 夕阳余晖",
    expected: false, // 应该需要翻译，因为是中文
    description: "纯中文文本"
  },
  {
    text: "A young woman walking by the river, in the style of notion style",
    expected: true, // 不需要翻译，因为是英文
    description: "纯英文文本"
  },
  {
    text: "こんにちは世界, hello world",
    expected: false, // 应该需要翻译，因为包含日文
    description: "日英混合文本"
  },
  {
    text: "안녕하세요, hello world",
    expected: false, // 应该需要翻译，因为包含韩文
    description: "韩英混合文本"
  }
];

console.log("=== 翻译检测逻辑测试 ===\n");

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.description}`);
  console.log(`输入: "${testCase.text}"`);
  
  const result = isTextPrimarylyEnglish(testCase.text);
  const shouldTranslate = !result;
  
  console.log(`结果: 需要翻译 = ${shouldTranslate}`);
  console.log(`期望: 需要翻译 = ${!testCase.expected}`);
  console.log(`测试: ${result === testCase.expected ? '✅ 通过' : '❌ 失败'}`);
  console.log('---');
});
