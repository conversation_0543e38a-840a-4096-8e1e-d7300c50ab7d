'use client';

import { ImageCard, type ImageCardData } from '@/components/shared/image-card';
import CustomPagination from '@/components/shared/pagination';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

// Data interfaces based on our database schema
interface GeneratedImageData {
  id: string;
  uuid: string;
  userId: string;
  imageUrl: string;
  imageSize?: string;
  width?: number;
  height?: number;
  format: string;
  prompt: string;
  imageDescription?: string;
  modelName: string;
  modelParams?: any;
  generationTime?: number;
  status: number;
  isPublic: boolean;
  slug?: string;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse {
  images: GeneratedImageData[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  availableModels: string[];
}

const IMAGES_PER_PAGE = 15;

// Gallery Grid Skeleton Component
function GalleryGridSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
      {[...Array(IMAGES_PER_PAGE)].map((_, index) => (
        <Card key={index} className="overflow-hidden">
          <Skeleton className="aspect-[4/3] w-full" />
          <CardContent className="p-4">
            <Skeleton className="h-4 w-3/4 mb-2" />
            <Skeleton className="h-3 w-full mb-2" />
            <Skeleton className="h-3 w-2/3 mb-4" />
            <div className="flex gap-2">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 w-16" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

interface GalleryClientProps {
  currentPage?: number;
}

export function GalleryClient({ currentPage = 1 }: GalleryClientProps) {
  const t = useTranslations('HomePage.gallery');
  const [images, setImages] = useState<GeneratedImageData[]>([]);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPublicOnly] = useState(true); // Gallery only shows public images

  // Fetch images data
  const fetchImages = async (page: number = currentPage) => {
    setIsLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: IMAGES_PER_PAGE.toString(),
        ...(showPublicOnly && { isPublic: 'true' }), // Filter based on public status
      });

      const response = await fetch(`/api/images?${searchParams}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ApiResponse = await response.json();

      setImages(data.images);
      setTotal(data.total);
      setTotalPages(data.totalPages);
    } catch (err) {
      console.error('Failed to fetch images:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch images');
    } finally {
      setIsLoading(false);
    }
  };

  // Load images for current page
  useEffect(() => {
    fetchImages(currentPage);
  }, [currentPage]);

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="min-h-[400px] flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-foreground mb-4">
              {t('error')}
            </h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => fetchImages(1)}>
              <RefreshCw className="w-4 h-4 mr-2" />
              {t('retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Stats */}
      {!isLoading && (
        <div className="mb-6">
          <p className="text-sm text-muted-foreground">
            {t('totalImages', { count: total })} •{' '}
            {t('pageInfo', { current: currentPage, total: totalPages })}
          </p>
        </div>
      )}

      {/* Loading State */}
      {isLoading && <GalleryGridSkeleton />}

      {/* Empty State */}
      {!isLoading && images.length === 0 && !error && (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium text-foreground mb-2">
            {t('noImages')}
          </h3>
          <p className="text-muted-foreground">{t('noImagesDescription')}</p>
        </div>
      )}

      {/* Image Grid */}
      {!isLoading && images.length > 0 && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6 mb-8">
            {images.map((image) => (
              <ImageCard
                key={image.id}
                image={image as ImageCardData}
                showFilename={true}
                showDetailedMetadata={true}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center">
              <CustomPagination
                routePrefix="/gallery"
                totalPages={totalPages}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
