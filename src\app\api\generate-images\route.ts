import { randomUUID } from 'crypto';
import type { GenerateImageRequest } from '@/ai/image/lib/api-types';
import type { ProviderKey } from '@/ai/image/lib/provider-config';
import { consumeCredits, hasEnoughCredits } from '@/credits/credits';
import { getDb } from '@/db';
import { generatedImage } from '@/db/schema';
import { getSession } from '@/lib/server';
import { getLocaleFromRequest } from '@/lib/auth';
import { uploadFile } from '@/storage';
import { createFal } from '@ai-sdk/fal';
import { fireworks } from '@ai-sdk/fireworks';
import { openai } from '@ai-sdk/openai';
import { replicate } from '@ai-sdk/replicate';
import {
  type ImageModel,
  experimental_generateImage as generateImage,
} from 'ai';
import { nanoid } from 'nanoid';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * Intended to be slightly less than the maximum execution time allowed by the
 * runtime so that we can gracefully terminate our request.
 */
const TIMEOUT_MILLIS = 55 * 1000;

const DEFAULT_IMAGE_SIZE = '1024x1024';
const DEFAULT_ASPECT_RATIO = '1:1';

// Utility functions
const base64ToBuffer = (base64: string): Buffer => {
  return Buffer.from(base64, 'base64');
};

const generateFileName = (provider: string, modelId: string): string => {
  const randomId = nanoid(12); // 使用更长的 nanoid 来确保唯一性
  // 简化文件名，只保留必要信息
  return `${randomId}.png`;
};

const generateSlug = (): string => {
  return nanoid(8); // 缩短 slug 长度
};

const getImageDimensions = (
  size: string
): { width: number; height: number } => {
  const [width, height] = size.split('x').map(Number);
  return { width, height };
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return (
    Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  );
};

const fal = createFal({
  apiKey: process.env.FAL_API_KEY,
});

interface ProviderConfig {
  createImageModel: (modelId: string) => ImageModel;
  dimensionFormat: 'size' | 'aspectRatio';
}

const providerConfig: Record<ProviderKey, ProviderConfig> = {
  replicate: {
    createImageModel: replicate.image,
    dimensionFormat: 'size',
  },
};

const withTimeout = <T>(
  promise: Promise<T>,
  timeoutMillis: number
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error('Request timed out')), timeoutMillis)
    ),
  ]);
};

export async function POST(req: NextRequest) {
  const requestId = Math.random().toString(36).substring(7);
  const {
    prompt,
    originalPrompt,
    provider,
    modelId,
    isPublic = false,
  } = (await req.json()) as GenerateImageRequest;

  try {
    // 检查用户认证
    const session = await getSession();
    if (!session?.user?.id) {
      console.error(`Unauthorized request [requestId=${requestId}]`);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!prompt || !provider || !modelId || !providerConfig[provider]) {
      console.error(
        `Invalid request parameters [requestId=${requestId}]: prompt=${prompt}, provider=${provider}, modelId=${modelId}`
      );
      return NextResponse.json(
        { error: 'Invalid request parameters' },
        { status: 400 }
      );
    }

    // 获取当前用户的语言设置（用于日志记录）
    const currentLocale = getLocaleFromRequest(req);
    console.log(`[${requestId}] User locale: ${currentLocale}`);

    // 处理 simple-illu 模型的提示词格式
    let processedPrompt = prompt;
    if (modelId.includes('yiquan00/simple-illu')) {
      // 确保提示词前面有 simple-illu 前缀
      if (!processedPrompt.startsWith('simple-illu ')) {
        processedPrompt = `simple-illu ${processedPrompt}`;
      }
    }

    console.log(
      `Starting image generation [requestId=${requestId}, provider=${provider}, model=${modelId}, prompt="${processedPrompt}"]`
    );

    // 检查积分是否足够（每次生成消耗1积分）
    const requiredCredits = 1;
    const hasCredits = await hasEnoughCredits({
      userId: session.user.id,
      requiredCredits,
    });

    if (!hasCredits) {
      console.error(
        `Insufficient credits [requestId=${requestId}, userId=${session.user.id}]`
      );
      return NextResponse.json(
        {
          error:
            'Insufficient credits. Please purchase more credits to continue.',
        },
        { status: 402 }
      );
    }

    const config = providerConfig[provider];
    const startstamp = performance.now();
    const generatePromise = generateImage({
      model: config.createImageModel(modelId),
      prompt: processedPrompt,
      ...(config.dimensionFormat === 'size'
        ? { size: DEFAULT_IMAGE_SIZE }
        : { aspectRatio: DEFAULT_ASPECT_RATIO }),
      ...(provider !== 'replicate' && {
        seed: Math.floor(Math.random() * 1000000),
      }),
      // Vertex AI only accepts a specified seed if watermark is disabled.
      providerOptions: { vertex: { addWatermark: false } },
    }).then(async ({ image, warnings }) => {
      if (warnings?.length > 0) {
        console.warn(
          `Warnings [requestId=${requestId}, provider=${provider}, model=${modelId}]: `,
          warnings
        );
      }

      const generationTime = Math.round(performance.now() - startstamp);
      console.log(
        `Completed image request [requestId=${requestId}, provider=${provider}, model=${modelId}, elapsed=${(
          generationTime / 1000
        ).toFixed(1)}s].`
      );

      // 上传到 R2 存储
      const imageBuffer = base64ToBuffer(image.base64);
      const fileName = generateFileName(provider, modelId);
      const slug = generateSlug();
      const uuid = randomUUID();
      const dimensions = getImageDimensions(DEFAULT_IMAGE_SIZE);

      console.log(`\n=== 开始处理图片保存 ===`);
      console.log(`Request ID: ${requestId}`);
      console.log(`User ID: ${session.user.id}`);
      console.log(`Provider: ${provider}`);
      console.log(`Model: ${modelId}`);
      console.log(`Prompt: ${prompt}`);
      console.log(`Generated file name: ${fileName}`);
      console.log(`Generated slug: ${slug}`);
      console.log(`Generated UUID: ${uuid}`);
      console.log(`Image buffer size: ${formatFileSize(imageBuffer.length)}`);
      console.log(`Image dimensions: ${dimensions.width}x${dimensions.height}`);

      try {
        console.log(`\n--- 开始上传到 R2 存储 ---`);
        console.log(`File name: ${fileName}`);
        console.log(`Content type: image/png`);
        console.log(`Buffer size: ${imageBuffer.length} bytes`);
        console.log(`Folder: gen`);
        console.log(`R2 Config Check:`);
        console.log(
          `  - STORAGE_REGION: ${process.env.STORAGE_REGION || 'NOT SET'}`
        );
        console.log(
          `  - STORAGE_BUCKET_NAME: ${process.env.STORAGE_BUCKET_NAME || 'NOT SET'}`
        );
        console.log(
          `  - STORAGE_ACCESS_KEY_ID: ${process.env.STORAGE_ACCESS_KEY_ID ? 'SET' : 'NOT SET'}`
        );
        console.log(
          `  - STORAGE_SECRET_ACCESS_KEY: ${process.env.STORAGE_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET'}`
        );
        console.log(
          `  - STORAGE_ENDPOINT: ${process.env.STORAGE_ENDPOINT || 'NOT SET'}`
        );
        console.log(
          `  - STORAGE_PUBLIC_URL: ${process.env.STORAGE_PUBLIC_URL || 'NOT SET'}`
        );

        const uploadResult = await uploadFile(
          imageBuffer,
          fileName,
          'image/png',
          'gen'
        );

        console.log(`Upload result:`, {
          url: uploadResult.url,
          key: uploadResult.key,
        });

        if (!uploadResult.url) {
          throw new Error(`R2 upload failed: No URL returned`);
        }

        console.log(`✅ R2 upload successful!`);
        console.log(`📁 File URL: ${uploadResult.url}`);

        // 保存到数据库
        console.log(`\n--- 开始保存到数据库 ---`);
        const imageRecord = {
          id: randomUUID(),
          uuid,
          userId: session.user.id,
          imageUrl: uploadResult.url,
          imageSize: formatFileSize(imageBuffer.length),
          width: dimensions.width,
          height: dimensions.height,
          format: 'PNG',
          prompt: originalPrompt, // 存储用户的原始输入
          imageDescription: null,
          modelName: `${provider}:${modelId}`,
          modelParams: null,
          generationTime,
          status: 1,
          isPublic: isPublic,
          slug,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        console.log(`Database record to insert:`, {
          id: imageRecord.id,
          uuid: imageRecord.uuid,
          userId: imageRecord.userId,
          imageUrl: imageRecord.imageUrl,
          imageSize: imageRecord.imageSize,
          dimensions: `${imageRecord.width}x${imageRecord.height}`,
          format: imageRecord.format,
          prompt:
            imageRecord.prompt.substring(0, 100) +
            (imageRecord.prompt.length > 100 ? '...' : ''),
          modelName: imageRecord.modelName,
          generationTime: `${imageRecord.generationTime}ms`,
          status: imageRecord.status,
          isPublic: imageRecord.isPublic,
          slug: imageRecord.slug,
        });

        const db = await getDb();
        await db.insert(generatedImage).values(imageRecord);

        console.log(`✅ Database save successful!`);
        console.log(`🆔 Record ID: ${imageRecord.id}`);

        // 扣除积分（图片生成成功后）
        try {
          await consumeCredits({
            userId: session.user.id,
            amount: requiredCredits,
            description: `AI Image Generation (${provider}:${modelId})`,
          });
          console.log(`💰 Credits consumed: ${requiredCredits} credit(s)`);
        } catch (creditError) {
          console.error(`❌ Failed to consume credits:`, creditError);
          // 注意：即使积分扣除失败，我们也不回滚图片生成，因为图片已经生成并保存
          // 这是为了避免用户体验问题，但应该记录错误以便后续处理
        }

        const finalResult = {
          provider,
          image: image.base64,
          savedImage: {
            ...imageRecord,
            createdAt: imageRecord.createdAt.toISOString(),
            updatedAt: imageRecord.updatedAt.toISOString(),
          },
        };

        console.log(`\n=== 图片处理完成 ===`);
        console.log(`🎉 成功生成并保存图片!`);
        console.log(`📊 处理统计:`);
        console.log(`   - 生成时间: ${generationTime}ms`);
        console.log(`   - 文件大小: ${formatFileSize(imageBuffer.length)}`);
        console.log(`   - 图片尺寸: ${dimensions.width}x${dimensions.height}`);
        console.log(`   - R2 URL: ${uploadResult.url}`);
        console.log(`   - 详情页面: /image/${slug}`);
        console.log(`   - 数据库 ID: ${imageRecord.id}`);
        console.log(`========================\n`);

        return finalResult;
      } catch (uploadError) {
        console.error(`\n❌ 图片保存失败!`);
        console.error(`Request ID: ${requestId}`);
        console.error(`Error details:`, uploadError);
        console.error(`Provider: ${provider}`);
        console.error(`Model: ${modelId}`);
        console.error(`File name: ${fileName}`);
        console.error(`========================\n`);

        // 即使保存失败，仍然返回生成的图片
        return {
          provider,
          image: image.base64,
          saveError: 'Failed to save image to storage',
        };
      }
    });

    const result = await withTimeout(generatePromise, TIMEOUT_MILLIS);
    return NextResponse.json(result, {
      status: 'image' in result ? 200 : 500,
    });
  } catch (error) {
    // Log full error detail on the server, but return a generic error message
    // to avoid leaking any sensitive information to the client.
    console.error(
      `Error generating image [requestId=${requestId}, provider=${provider}, model=${modelId}]: `,
      error
    );
    return NextResponse.json(
      {
        error: 'Failed to generate image. Please try again later.',
      },
      { status: 500 }
    );
  }
}
