'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar, Check, Copy, File, Globe, Lock } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

// Common image data type
export interface ImageCardData {
  id: string;
  filename?: string;
  imageUrl: string;
  prompt: string;
  originalDescription?: string;
  modelName?: string;
  isPublic?: boolean;
  slug?: string;
  createdAt: string;
  imageSize?: string;
  format?: string;
}

interface ImageCardProps {
  image: ImageCardData;
  showFilename?: boolean; // 是否显示文件名
  showDetailedMetadata?: boolean; // 是否显示详细元数据
  className?: string;
}

export function ImageCard({
  image,
  showFilename = true,
  showDetailedMetadata = true,
  className = '',
}: ImageCardProps) {
  const t = useTranslations('HomePage.dashboard.myImages');
  const [copiedStates, setCopiedStates] = useState<{ [key: string]: boolean }>(
    {}
  );

  const copyToClipboard = async (
    text: string,
    id: string,
    type: 'url' | 'md'
  ) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates((prev) => ({ ...prev, [`${id}-${type}`]: true }));
      setTimeout(() => {
        setCopiedStates((prev) => ({ ...prev, [`${id}-${type}`]: false }));
      }, 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getMarkdownFormat = (image: ImageCardData) => {
    const title = image.originalDescription || image.prompt || '生成图片';
    return `![${title}](${image.imageUrl})`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${month}-${day} ${hours}:${minutes}`;
  };

  const formatModelName = (modelName: string) => {
    return modelName.split('/').pop() || modelName;
  };

  const formatFileSize = (size?: string) => {
    if (!size) return '';
    return size;
  };

  return (
    <Card
      className={`overflow-hidden hover:shadow-lg transition-shadow group ${className}`}
    >
      {/* Clickable image area */}
      <Link
        href={image.slug ? `/image/${image.slug}` : '#'}
        className="block"
        target="_blank"
        rel="noopener noreferrer"
      >
        <div className="relative aspect-[4/3] bg-gray-100 group-hover:scale-105 transition-transform duration-200">
          <Image
            src={image.imageUrl || '/placeholder.svg'}
            alt={image.prompt}
            fill
            className="object-cover"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
          />
          {/* Public/Private status indicator */}
          {image.isPublic !== undefined && (
            <div className="absolute top-2 right-2">
              <Badge
                variant={image.isPublic ? 'default' : 'secondary'}
                className="text-xs"
              >
                {image.isPublic ? (
                  <>
                    <Globe className="w-3 h-3 mr-1" />
                    {t('public')}
                  </>
                ) : (
                  <>
                    <Lock className="w-3 h-3 mr-1" />
                    {t('private')}
                  </>
                )}
              </Badge>
            </div>
          )}
          {/* 悬停提示 */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-background/90 border px-3 py-1 rounded-full text-sm font-medium text-foreground shadow-sm">
              {t('viewDetails')}
            </div>
          </div>
        </div>
      </Link>

      <CardContent className="p-4">
        {/* 文件名 */}
        {showFilename && image.filename && (
          <h3
            className="font-medium text-sm text-foreground mb-2 truncate"
            title={image.filename}
          >
            {image.filename}
          </h3>
        )}

        {/* 提示词作为标题 */}
        <h4
          className="font-semibold text-sm text-foreground mb-3 line-clamp-2"
          title={image.prompt}
        >
          {image.prompt}
        </h4>

        {/* 元数据 */}
        {showDetailedMetadata ? (
          <div className="space-y-1 mb-4">
            <div className="flex items-center text-xs text-muted-foreground">
              <Calendar className="w-3 h-3 mr-1" />
              {formatDate(image.createdAt)}
            </div>
            {image.imageSize && image.format && (
              <div className="flex items-center text-xs text-muted-foreground">
                <File className="w-3 h-3 mr-1" />
                {formatFileSize(image.imageSize)} • {image.format}
              </div>
            )}
            {/* 隐藏模型名称显示 */}
            {/* <div
              className="text-xs text-primary truncate"
              title={image.modelName}
            >
              {formatModelName(image.modelName)}
            </div> */}
          </div>
        ) : (
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
            {/* 隐藏模型名称显示 */}
            {/* <span>{formatModelName(image.modelName)}</span> */}
            <span>{formatDate(image.createdAt)}</span>
          </div>
        )}

        {/* 复制按钮 */}
        <div className="flex justify-end">
          <Popover>
            <PopoverTrigger asChild>
              <Button size="sm" variant="outline" className="w-8 h-8 p-0">
                {Object.keys(copiedStates).some(
                  (key) => key.startsWith(image.id) && copiedStates[key]
                ) ? (
                  <Check className="w-3 h-3 text-green-600" />
                ) : (
                  <Copy className="w-3 h-3" />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-2">
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() =>
                    copyToClipboard(image.imageUrl, image.id, 'url')
                  }
                >
                  <Copy className="w-3 h-3 mr-2" />
                  {t('copyUrl')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() =>
                    copyToClipboard(getMarkdownFormat(image), image.id, 'md')
                  }
                >
                  <Copy className="w-3 h-3 mr-2" />
                  {t('copyMarkdown')}
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </CardContent>
    </Card>
  );
}
