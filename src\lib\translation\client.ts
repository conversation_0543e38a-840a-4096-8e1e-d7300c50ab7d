/**
 * 前端翻译服务客户端
 * 
 * 通过API路由调用后端翻译服务
 */

export interface TranslationResult {
  original: string;
  translated: string;
  wasTranslated: boolean;
  sourceLanguage?: string;
  targetLanguage?: string;
  reason?: string;
}

/**
 * 翻译文本（前端使用）
 * @param text 要翻译的文本
 * @param locale 用户当前语言环境
 * @returns 翻译结果
 */
export async function translateTextClient(
  text: string,
  locale: string
): Promise<TranslationResult> {
  try {
    const response = await fetch('/api/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text, locale }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const result: TranslationResult = await response.json();
    return result;
  } catch (error) {
    console.error('[Client] Translation error:', error);
    
    // 翻译失败时返回原文
    return {
      original: text,
      translated: text,
      wasTranslated: false,
      reason: `Translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * 检查文本是否主要包含英文（前端使用）
 */
export function isTextPrimarylyEnglishClient(text: string): boolean {
  // 移除标点符号、数字和空格，只保留字母
  const letters = text.replace(/[^\p{L}]/gu, '');
  if (letters.length === 0) return true; // 没有字母的情况默认为英文
  
  // 计算英文字母的比例
  const englishLetters = letters.match(/[a-zA-Z]/g);
  const englishRatio = englishLetters ? englishLetters.length / letters.length : 0;
  
  // 检查是否包含中文、日文、韩文等字符
  const cjkPattern = /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af]/;
  const hasCJK = cjkPattern.test(text);
  
  // 如果包含中日韩文字，即使英文比例高也需要翻译
  if (hasCJK) {
    console.log(`[Translation] Text contains CJK characters, will translate despite English ratio: ${englishRatio}`);
    return false;
  }
  
  // 如果英文字母占比超过90%，认为是英文文本
  const isEnglish = englishRatio > 0.9;
  console.log(`[Translation] English ratio: ${englishRatio}, is English: ${isEnglish}`);
  return isEnglish;
}
