export interface Suggestion {
  text: string;
  prompt: string;
}

const artStyles = [
  'minimalist illustration',
  'flat design',
  'notion style',
  'clean vector art',
  'modern illustration',
  'geometric style',
];

const basePrompts: { text: string; prompt: string }[] = [
  {
    text: 'Girl Reading',
    prompt: 'A young woman reading a book in a cozy cafe by the window',
  },
  {
    text: 'Man Coding',
    prompt:
      'A developer coding late at night with multiple monitors and coffee',
  },
  {
    text: 'Girl Painting',
    prompt: 'An artist painting on a canvas in a bright sunlit studio',
  },
  {
    text: 'Woman Meditating',
    prompt: 'A woman meditating peacefully in a minimalist room with plants',
  },
  {
    text: 'Man Cooking',
    prompt: 'A chef preparing fresh ingredients in a modern kitchen',
  },
  {
    text: 'Girl Dancing',
    prompt: 'A dancer practicing ballet moves in an empty dance studio',
  },
  {
    text: 'Woman Working',
    prompt: 'A professional woman working on her laptop in a modern office',
  },
  {
    text: 'Man Gardening',
    prompt: 'A person tending to plants in a rooftop garden at sunrise',
  },
  {
    text: 'Girl Writing',
    prompt:
      'A writer journaling in a notebook at a wooden desk with warm lighting',
  },
  {
    text: 'Woman Exercising',
    prompt: 'A person doing yoga poses on a mat in a serene outdoor setting',
  },
  {
    text: 'Man Teaching',
    prompt:
      'A teacher explaining concepts at a whiteboard in a bright classroom',
  },
  {
    text: 'Girl Studying',
    prompt:
      'A student studying with books and notes spread across a library table',
  },
  {
    text: 'Woman Traveling',
    prompt: 'A traveler with a backpack exploring a scenic mountain trail',
  },
  {
    text: 'Man Presenting',
    prompt: 'A professional giving a presentation in a modern conference room',
  },
  {
    text: 'Girl Shopping',
    prompt: 'A person browsing through a charming local bookstore',
  },
  {
    text: 'Woman Listening',
    prompt: 'A person wearing headphones and enjoying music in a park',
  },
  {
    text: 'Man Building',
    prompt: 'An architect reviewing blueprints at a construction site',
  },
  {
    text: 'Girl Cycling',
    prompt: 'A cyclist riding through a tree-lined path in golden hour light',
  },
  {
    text: 'Woman Sketching',
    prompt: 'An artist sketching in a notebook while sitting on a park bench',
  },
  {
    text: 'Man Thinking',
    prompt: 'A person deep in thought while walking along a quiet beach',
  },
  {
    text: 'Girl Baking',
    prompt: 'A baker kneading dough in a warm, flour-dusted kitchen',
  },
  {
    text: 'Woman Running',
    prompt:
      'A runner jogging through a misty forest trail in the early morning',
  },
  {
    text: 'Man Playing',
    prompt: 'A musician playing guitar on a rooftop overlooking the city',
  },
];

function shuffle<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

export function getRandomSuggestions(count = 5): Suggestion[] {
  const shuffledPrompts = shuffle(basePrompts);
  const shuffledStyles = shuffle(artStyles);

  return shuffledPrompts.slice(0, count).map((item, index) => ({
    text: item.text,
    prompt: `${item.prompt}, in the style of ${
      shuffledStyles[index % shuffledStyles.length]
    }`,
  }));
}
