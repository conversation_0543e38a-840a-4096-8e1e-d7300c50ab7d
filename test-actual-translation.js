/**
 * 测试实际的翻译功能
 */

async function testActualTranslation() {
  // 导入翻译函数
  const { translateText, isTextPrimarylyEnglish } = await import('./src/lib/translation/deeplx.ts');
  const { getDeepLXLanguageCode, shouldTranslateToEnglish } = await import('./src/lib/translation/language-mapping.ts');

  const testPrompt = "一个年轻女子在河边散步， 夕阳余晖, in the style of notion style, in the style of flat design,monochrome, just black and white";
  const userLocale = "zh";

  console.log("=== 实际翻译功能测试 ===\n");
  console.log(`用户语言: ${userLocale}`);
  console.log(`输入提示词: "${testPrompt}"`);
  console.log();

  // 1. 检查是否需要翻译
  const needsTranslation = shouldTranslateToEnglish(userLocale) && !isTextPrimarylyEnglish(testPrompt);
  console.log(`是否需要翻译: ${needsTranslation}`);
  
  if (needsTranslation) {
    const sourceLanguage = getDeepLXLanguageCode(userLocale);
    console.log(`源语言代码: ${sourceLanguage}`);
    
    try {
      console.log("开始翻译...");
      const translatedPrompt = await translateText(testPrompt, sourceLanguage, 'EN');
      
      console.log();
      console.log("=== 翻译结果 ===");
      console.log(`原文: "${testPrompt}"`);
      console.log(`译文: "${translatedPrompt}"`);
      console.log(`翻译成功: ${translatedPrompt !== testPrompt ? '✅' : '❌'}`);
      
    } catch (error) {
      console.error("翻译失败:", error);
    }
  } else {
    console.log("跳过翻译");
  }
}

testActualTranslation().catch(console.error);
