/**
 * 快速测试DeepLX新API的脚本
 * 运行: node scripts/quick-translation-test.js
 */

const DEEPLX_API_URL = "https://api.deeplx.org/kagMDqywXSrS0pNB7R4BGYQWWO6jiLUJX_9-AE2wMW0/translate";

async function testNewDeepLXAPI() {
  console.log('🧪 Testing new DeepLX API...');
  
  const testText = "一只可爱的小猫";
  const requestBody = {
    text: testText,
    source_lang: "ZH",
    target_lang: "EN"
  };
  
  try {
    console.log(`📤 Sending request to: ${DEEPLX_API_URL}`);
    console.log(`📝 Request body:`, requestBody);
    
    const response = await fetch(DEEPLX_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log(`📥 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ HTTP Error: ${response.status} - ${errorText}`);
      return;
    }
    
    const result = await response.json();
    console.log(`📋 Response data:`, result);
    
    if (result.code === 200 && result.data) {
      console.log(`✅ Translation successful!`);
      console.log(`   Original: "${testText}"`);
      console.log(`   Translated: "${result.data}"`);
    } else {
      console.log(`❌ Translation failed:`, result.message || 'Unknown error');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testNewDeepLXAPI();
