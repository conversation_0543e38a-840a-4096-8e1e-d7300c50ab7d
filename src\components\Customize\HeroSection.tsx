'use client';

import { TypingAnimation } from '@/components/magicui/typing-animation';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import type React from 'react';

interface HeroSectionProps {
  title?: string;
  description?: string;
  className?: string;
}

// 背景装饰插画元素配置
const decorativeIllustrations = [
  {
    src: '/examples/notion-example-1.png',
    position: 'top-20 right-10',
    size: 'w-32 h-32',
    rotation: 'rotate-6',
    opacity: 'opacity-60',
    animation: 'float-animation-slow',
  },
  {
    src: '/examples/notion-example-2.png',
    position: 'bottom-20 left-10',
    size: 'w-40 h-40',
    rotation: '-rotate-3',
    opacity: 'opacity-60',
    animation: 'float-animation-medium',
  },
  {
    src: '/examples/notion-example-3.png',
    position: 'top-40 left-20',
    size: 'w-36 h-36',
    rotation: 'rotate-12',
    opacity: 'opacity-50',
    animation: 'float-animation-slow',
  },
  {
    src: '/examples/notion-example-4.png',
    position: 'bottom-40 right-20',
    size: 'w-28 h-28',
    rotation: '-rotate-6',
    opacity: 'opacity-60',
    animation: 'float-animation-medium',
  },
  {
    src: '/examples/notion-example-1.png',
    position: 'top-1/4 right-1/4',
    size: 'w-16 h-16',
    rotation: 'rotate-12',
    opacity: 'opacity-40',
    animation: 'float-animation-medium',
  },
  {
    src: '/examples/notion-example-2.png',
    position: 'bottom-1/4 left-1/3',
    size: 'w-20 h-20',
    rotation: '-rotate-6',
    opacity: 'opacity-40',
    animation: 'float-animation-slow',
  },
  {
    src: '/examples/notion-example-3.png',
    position: 'top-1/3 left-1/4',
    size: 'w-24 h-24',
    rotation: 'rotate-3',
    opacity: 'opacity-30',
    animation: 'float-animation-medium',
  },
  {
    src: '/examples/notion-example-4.png',
    position: 'bottom-1/3 right-1/3',
    size: 'w-20 h-20',
    rotation: '-rotate-12',
    opacity: 'opacity-40',
    animation: 'float-animation-slow',
  },
];

const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  description,
  className = '',
}) => {
  const t = useTranslations('HomePage.hero');
  return (
    <section className={`py-20 md:py-32 relative overflow-hidden ${className}`}>
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-muted/50 to-background -z-10" />
      <div className="absolute top-0 right-0 w-[500px] h-[500px] bg-muted/30 rounded-full blur-3xl -z-10 opacity-60" />
      <div className="absolute bottom-0 left-0 w-[500px] h-[500px] bg-muted/40 rounded-full blur-3xl -z-10 opacity-60" />

      {/* Decorative illustrations */}
      {decorativeIllustrations.map((illustration, index) => (
        <div
          key={index}
          className={`absolute ${illustration.position} ${illustration.size} ${illustration.rotation} ${illustration.opacity} ${illustration.animation} z-0 pointer-events-none transition-all duration-500 hover:opacity-100 hover:scale-105`}
        >
          <div className="relative w-full h-full">
            <Image
              src={illustration.src}
              alt="Notion-style illustration"
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, 400px"
            />
          </div>
        </div>
      ))}

      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 relative z-10">
          {/* Left column - Content */}
          <div className="w-full lg:w-1/2 text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
              <span className="gradient-text">{title ?? t('title')}</span>
            </h1>

            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-lg mx-auto lg:mx-0">
              {description ?? t('description')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href="/dashboard">
                <Button
                  variant="default"
                  size="lg"
                  className="bg-primary hover:bg-primary/90 text-white"
                >
                  {t('primary')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="/gallery">
                <Button variant="outline" size="lg">
                  {t('secondary')}
                </Button>
              </Link>
            </div>
          </div>

          {/* Right column - Illustration */}
          <div className="w-full lg:w-1/2">
            <div className="relative bg-background border rounded-xl shadow-lg dark:shadow-zinc-950/25 p-6 max-w-md mx-auto">
              <div className="border-b border-border pb-4 mb-4">
                <div className="flex space-x-2 absolute top-4 left-4">
                  <div className="w-3 h-3 bg-red-400 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-400 rounded-full" />
                  <div className="w-3 h-3 bg-green-400 rounded-full" />
                </div>
                <div className="text-center pt-2">
                  <p className="text-sm text-muted-foreground">
                    Notion-Style Illustration Generator
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-muted rounded-lg p-4">
                  <p className="text-sm text-muted-foreground mb-2">Prompt:</p>
                  <div className="h-8 flex items-center">
                    <TypingAnimation className="text-foreground font-medium text-sm">
                      girl reading a book
                    </TypingAnimation>
                  </div>
                </div>

                <div className="flex justify-center space-x-3">
                  <Link href="/dashboard">
                    <Button variant="outline" size="sm">
                      {t('rightButton')}
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-6 -right-6 bg-card p-3 rounded-lg shadow-md dark:shadow-zinc-950/25 rotate-12 hidden md:block">
                <div className="w-12 h-3 bg-primary/20 rounded-md" />
              </div>
              <div className="absolute -bottom-6 -left-6 bg-card p-3 rounded-lg shadow-md dark:shadow-zinc-950/25 -rotate-12 hidden md:block">
                <div className="w-12 h-12 bg-primary/10 rounded-full" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
