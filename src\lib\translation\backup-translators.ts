/**
 * 备选翻译服务 - 使用免费的翻译API
 * 
 * 当 DeepLX API Key 不可用时的备选方案
 */

export interface LibreTranslateRequest {
  q: string;
  source: string;
  target: string;
  format?: string;
}

export interface LibreTranslateResponse {
  translatedText: string;
}

/**
 * 使用 LibreTranslate (免费开源翻译服务) 进行翻译
 * 公共实例: https://libretranslate.com/
 */
export async function translateWithLibreTranslate(
  text: string,
  sourceLang: string,
  targetLang = 'en'
): Promise<string> {
  // 简化语言代码映射
  const langMapping: Record<string, string> = {
    'ZH': 'zh',
    'JA': 'ja',
    'EN': 'en',
    'KO': 'ko',
    'FR': 'fr',
    'DE': 'de',
    'ES': 'es',
    'IT': 'it',
    'PT': 'pt',
    'RU': 'ru',
  };

  const sourceCode = langMapping[sourceLang.toUpperCase()] || sourceLang.toLowerCase();
  const targetCode = langMapping[targetLang.toUpperCase()] || targetLang.toLowerCase();

  if (sourceCode === targetCode) {
    return text;
  }

  try {
    console.log(`[LibreTranslate] Translating from ${sourceCode} to ${targetCode}: "${text.substring(0, 50)}..."`);

    const requestBody: LibreTranslateRequest = {
      q: text,
      source: sourceCode,
      target: targetCode,
      format: 'text'
    };

    const response = await fetch('https://libretranslate.com/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[LibreTranslate] HTTP Error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result: LibreTranslateResponse = await response.json();
    
    if (result.translatedText) {
      console.log(`[LibreTranslate] Translation successful: "${result.translatedText.substring(0, 50)}..."`);
      return result.translatedText;
    }
    
    console.error('[LibreTranslate] No translation result');
    return text;
  } catch (error) {
    console.error('[LibreTranslate] Translation error:', error);
    return text;
  }
}

/**
 * 使用 MyMemory 翻译服务 (另一个免费选择)
 */
export async function translateWithMyMemory(
  text: string,
  sourceLang: string,
  targetLang = 'en'
): Promise<string> {
  const sourceCode = sourceLang.toLowerCase();
  const targetCode = targetLang.toLowerCase();

  if (sourceCode === targetCode) {
    return text;
  }

  try {
    console.log(`[MyMemory] Translating from ${sourceCode} to ${targetCode}: "${text.substring(0, 50)}..."`);

    const encodedText = encodeURIComponent(text);
    const url = `https://api.mymemory.translated.net/get?q=${encodedText}&langpair=${sourceCode}|${targetCode}`;

    const response = await fetch(url, {
      method: 'GET',
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.responseData?.translatedText) {
      const translatedText = result.responseData.translatedText;
      console.log(`[MyMemory] Translation successful: "${translatedText.substring(0, 50)}..."`);
      return translatedText;
    }
    
    console.error('[MyMemory] No translation result');
    return text;
  } catch (error) {
    console.error('[MyMemory] Translation error:', error);
    return text;
  }
}
