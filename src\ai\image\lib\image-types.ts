import type { Provider<PERSON><PERSON> } from './provider-config';
import type { SavedImageData } from './api-types';

export interface GeneratedImage {
  provider: ProviderKey;
  image: string | null;
  modelId?: string;
}

export interface ImageResult {
  provider: ProviderKey;
  image: string | null;
  modelId?: string;
  savedImage?: SavedImageData;
  saveError?: string;
}

export interface ImageError {
  provider: ProviderKey;
  message: string;
}

export interface ProviderTiming {
  startTime?: number;
  completionTime?: number;
  elapsed?: number;
}
