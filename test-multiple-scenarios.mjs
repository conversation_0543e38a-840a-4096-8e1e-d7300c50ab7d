/**
 * 测试纯中文翻译
 */
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 翻译函数
async function translateText(text, sourceLang, targetLang = 'EN') {
  if (sourceLang.toUpperCase() === targetLang.toUpperCase()) {
    return text;
  }

  if (!process.env.DEEPLX_API_URL) {
    console.warn('DEEPLX_API_URL not configured, skipping translation');
    return text;
  }

  try {
    console.log(`[DeepLX] Translating from ${sourceLang} to ${targetLang}: "${text}"`);

    const requestBody = {
      text,
      source_lang: sourceLang.toUpperCase(),
      target_lang: targetLang.toUpperCase()
    };

    const response = await fetch(process.env.DEEPLX_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(10000)
    });

    console.log(`[DeepLX] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[DeepLX] HTTP Error: ${response.status} - ${errorText}`);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('[DeepLX] API Response:', result);
    
    if (result.code === 200 && result.data) {
      console.log(`[DeepLX] Translation successful: "${result.data}"`);
      return result.data;
    }
    
    console.error('[DeepLX] Translation failed:', result.message || 'Unknown error', result);
    return text;
  } catch (error) {
    console.error('[DeepLX] Translation error:', error);
    return text;
  }
}

// 测试多个场景
async function runTests() {
  console.log("=== 多场景翻译测试 ===\n");

  const testCases = [
    {
      name: "纯中文",
      text: "一个年轻女子在河边散步，夕阳余晖",
      sourceLang: "ZH",
      expected: "should be translated"
    },
    {
      name: "中英混合 - 中文为主",
      text: "一个年轻女子在河边散步，夕阳余晖，flat design style",
      sourceLang: "ZH", 
      expected: "should be translated"
    },
    {
      name: "原问题的混合文本",
      text: "一个年轻女子在河边散步， 夕阳余晖, in the style of notion style, in the style of flat design,monochrome, just black and white",
      sourceLang: "ZH",
      expected: "partial translation expected"
    },
    {
      name: "AUTO 检测语言",
      text: "一个年轻女子在河边散步，夕阳余晖",
      sourceLang: "AUTO",
      expected: "should auto-detect and translate"
    }
  ];

  for (const testCase of testCases) {
    console.log(`--- 测试: ${testCase.name} ---`);
    console.log(`输入: "${testCase.text}"`);
    console.log(`源语言: ${testCase.sourceLang}`);
    console.log(`预期: ${testCase.expected}`);
    
    try {
      const result = await translateText(testCase.text, testCase.sourceLang, 'EN');
      const wasTranslated = result !== testCase.text;
      
      console.log(`结果: "${result}"`);
      console.log(`翻译状态: ${wasTranslated ? '✅ 已翻译' : '⚠️  未翻译'}`);
    } catch (error) {
      console.error(`❌ 翻译失败:`, error.message);
    }
    
    console.log('');
    
    // 添加延迟避免API限制
    await new Promise(resolve => setTimeout(resolve, 1000));  
  }
}

runTests().catch(console.error);
