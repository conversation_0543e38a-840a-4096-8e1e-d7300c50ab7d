'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import {
  Download,
  Copy,
  FileText,
  Check,
  Clock,
  Sparkles,
  Settings,
  Globe,
  Lock,
  Palette,
  ExternalLink,
  User,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// Common image data type
export interface ImageDetailData {
  id: string;
  filename: string;
  imageUrl: string;
  title?: string;
  prompt: string;
  imageDescription?: string;
  originalDescription?: string;
  modelName: string;
  modelParams?: any;
  generationTime?: number;
  status?: number;
  isPublic?: boolean;
  slug?: string;
  createdAt: string;
  updatedAt?: string;
  // Optional creator information (for Gallery)
  creator?: {
    name: string;
    avatar?: string;
    username?: string;
  };
  // Optional file information
  imageSize?: string;
  width?: number;
  height?: number;
  format?: string;
}

interface ImageDetailViewProps {
  image: ImageDetailData;
  showCreator?: boolean; // Whether to show creator information
  showModelInfo?: boolean; // Whether to show model information
  showPrivacyStatus?: boolean; // Whether to show public/private status
  showExternalLink?: boolean; // Whether to show external link button
  className?: string;
}

export function ImageDetailView({
  image,
  showCreator = false,
  showModelInfo = false, // 默认隐藏模型信息
  showPrivacyStatus = true,
  showExternalLink = true,
  className = '',
}: ImageDetailViewProps) {
  const t = useTranslations('HomePage.imageDetail');
  const [copiedStates, setCopiedStates] = useState<{ [key: string]: boolean }>({});

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates((prev) => ({ ...prev, [type]: true }));
      setTimeout(() => {
        setCopiedStates((prev) => ({ ...prev, [type]: false }));
      }, 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const downloadImage = async (url: string, filename: string) => {
    try {
      // Use API route proxy for download to avoid CORS issues
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: url }),
      });

      if (!response.ok) {
        throw new Error('Download failed');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (err) {
      console.error('Failed to download: ', err);
      // If API route fails, try direct download (may have CORS issues)
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackErr) {
        console.error('Fallback download also failed: ', fallbackErr);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  const formatModelName = (modelName: string) => {
    return modelName.split('/').pop() || modelName;
  };

  const getMarkdownUrl = () => {
    const title = image.title || image.originalDescription || t('generatedImage');
    return `![${title}](${image.imageUrl})`;
  };

  return (
    <div className={`grid lg:grid-cols-2 gap-8 ${className}`}>
      {/* Image preview area */}
      <div className="space-y-6">
        <div className="relative bg-muted rounded-lg overflow-hidden border flex items-center justify-center min-h-[400px]">
          <Image
            src={image.imageUrl || '/placeholder.svg'}
            alt={image.prompt}
            width={image.width || 1024}
            height={image.height || 1024}
            className="object-contain max-w-full max-h-full"
            sizes="(max-width: 1024px) 100vw, 50vw"
          />
          {/* Public/Private status indicator */}
          {showPrivacyStatus && image.isPublic !== undefined && (
            <div className="absolute top-3 right-3">
              <Badge
                variant={image.isPublic ? 'default' : 'secondary'}
                className="text-xs shadow-sm"
              >
                {image.isPublic ? (
                  <>
                    <Globe className="w-3 h-3 mr-1.5" />
                    {t('public')}
                  </>
                ) : (
                  <>
                    <Lock className="w-3 h-3 mr-1.5" />
                    {t('private')}
                  </>
                )}
              </Badge>
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="space-y-3">
          {/* View details page button */}
          {showExternalLink && image.slug && (
            <Link href={`/image/${image.slug}`} target="_blank">
              <Button variant="default" size="sm" className="w-full h-10">
                <ExternalLink className="w-4 h-4 mr-2" />
                {t('viewDetails')}
              </Button>
            </Link>
          )}

          {/* Other action buttons */}
          <div className="grid grid-cols-3 gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => downloadImage(image.imageUrl, image.filename)}
              className="h-10"
            >
              <Download className="w-4 h-4 mr-2" />
              {t('download')}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(image.imageUrl, 'url')}
              className="h-10"
            >
              {copiedStates.url ? (
                <>
                  <Check className="w-4 h-4 mr-2 text-green-600" />
                  {t('copied')}
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 mr-2" />
                  {t('copyUrl')}
                </>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(getMarkdownUrl(), 'md')}
              className="h-10"
            >
              {copiedStates.md ? (
                <>
                  <Check className="w-4 h-4 mr-2 text-green-600" />
                  {t('copied')}
                </>
              ) : (
                <>
                  <FileText className="w-4 h-4 mr-2" />
                  {t('copyMarkdown')}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Details information area */}
      <div className="space-y-6">
        {/* Creator information */}
        {showCreator && image.creator && (
          <div className="space-y-3">
            <h4 className="font-semibold text-foreground flex items-center gap-2">
              <User className="w-4 h-4 text-muted-foreground" />
              {t('creatorInfo')}
            </h4>
            <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg border">
              <Avatar className="w-10 h-10">
                <AvatarImage
                  src={image.creator.avatar || '/placeholder.svg'}
                  alt={image.creator.name}
                />
                <AvatarFallback className="text-sm font-medium">
                  {image.creator.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium text-foreground">
                  {image.creator.name}
                </div>
                {image.creator.username && (
                  <div className="text-sm text-muted-foreground">
                    @{image.creator.username}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Creation time */}
        <div className="space-y-3">
          <h4 className="font-semibold text-foreground flex items-center gap-2">
            <Clock className="w-4 h-4 text-muted-foreground" />
            {t('creationTime')}
          </h4>
          <div className="p-4 bg-muted/50 rounded-lg border">
            <p className="text-sm text-foreground">{formatDate(image.createdAt)}</p>
          </div>
        </div>

        {/* Prompt */}
        <div className="space-y-3">
          <h4 className="font-semibold text-foreground flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-muted-foreground" />
            {t('creationPrompt')}
          </h4>
          <div className="bg-muted/50 p-4 rounded-lg border space-y-3">
            <p className="text-sm leading-relaxed text-foreground">{image.prompt}</p>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 -ml-1"
              onClick={() => copyToClipboard(image.prompt, 'prompt')}
            >
              {copiedStates.prompt ? (
                <>
                  <Check className="w-3 h-3 mr-1.5 text-green-600" />
                  {t('copiedPrompt')}
                </>
              ) : (
                <>
                  <Copy className="w-3 h-3 mr-1.5" />
                  {t('copyPrompt')}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Image description */}
        {image.imageDescription && (
          <div className="space-y-3">
            <h4 className="font-semibold text-foreground flex items-center gap-2">
              <Palette className="w-4 h-4 text-muted-foreground" />
              {t('imageDescription')}
            </h4>
            <div className="p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
              <p className="text-sm text-foreground leading-relaxed">{image.imageDescription}</p>
            </div>
          </div>
        )}

        {/* Model information */}
        {showModelInfo && (
          <div className="space-y-3">
            <h4 className="font-semibold text-foreground flex items-center gap-2">
              <Settings className="w-4 h-4 text-muted-foreground" />
              {t('modelInfo')}
            </h4>
            <div className="space-y-3">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                  <span className="text-sm text-muted-foreground">{t('model')}</span>
                  <span className="font-mono text-xs text-foreground font-medium">
                    {formatModelName(image.modelName)}
                  </span>
                </div>
                {image.generationTime && (
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                    <span className="text-sm text-muted-foreground">{t('generationTime')}</span>
                    <span className="text-sm text-foreground font-medium">{image.generationTime}{t('seconds')}</span>
                  </div>
                )}
                {image.width && image.height && (
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                    <span className="text-sm text-muted-foreground">{t('dimensions')}</span>
                    <span className="text-sm text-foreground font-medium">
                      {image.width} × {image.height}
                    </span>
                  </div>
                )}
                {image.imageSize && (
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                    <span className="text-sm text-muted-foreground">{t('fileSize')}</span>
                    <span className="text-sm text-foreground font-medium">{image.imageSize}</span>
                  </div>
                )}
                {image.format && (
                  <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                    <span className="text-sm text-muted-foreground">{t('format')}</span>
                    <span className="text-sm text-foreground font-medium">{image.format}</span>
                  </div>
                )}
              </div>
              {image.modelParams && (
                <div className="space-y-2">
                  <span className="text-sm text-muted-foreground font-medium">{t('modelParams')}</span>
                  <pre className="text-xs bg-muted/50 p-3 rounded-lg border overflow-x-auto font-mono text-foreground">
                    {JSON.stringify(image.modelParams, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
