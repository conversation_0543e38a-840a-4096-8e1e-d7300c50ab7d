/**
 * 翻译功能测试API端点
 * 访问: http://localhost:3002/api/test-translation
 */

import { NextRequest, NextResponse } from 'next/server';
import { translateText, isTextPrimarylyEnglish } from '@/lib/translation/deeplx';
import { getDeepLXLanguageCode, shouldTranslateToEnglish } from '@/lib/translation/language-mapping';

export async function GET(req: NextRequest) {
  try {
    console.log('🧪 Testing translation API...');
    
    // 测试用例
    const testCases = [
      { text: '一只可爱的小猫在花园里玩耍', locale: 'zh' },
      { text: '美しい桜の花が咲いています', locale: 'ja' },
      { text: 'A beautiful cat playing in the garden', locale: 'en' },
    ];
    
    const results = [];
    
    for (const testCase of testCases) {
      const needsTranslation = shouldTranslateToEnglish(testCase.locale);
      const isPrimarylyEnglish = isTextPrimarylyEnglish(testCase.text);
      const sourceLanguage = getDeepLXLanguageCode(testCase.locale);
      
      let translatedText = testCase.text;
      let translationTime = 0;
      let translationStatus = 'skipped';
      
      if (needsTranslation && !isPrimarylyEnglish) {
        const startTime = Date.now();
        try {
          translatedText = await translateText(testCase.text, sourceLanguage, 'EN');
          translationTime = Date.now() - startTime;
          translationStatus = translatedText !== testCase.text ? 'success' : 'failed';
        } catch (error) {
          translationStatus = 'error';
          console.error('Translation error:', error);
        }
      }
      
      results.push({
        original: testCase.text,
        locale: testCase.locale,
        sourceLanguage,
        needsTranslation,
        isPrimarylyEnglish,
        translated: translatedText,
        translationStatus,
        translationTime,
      });
    }
    
    return NextResponse.json({
      success: true,
      environment: {
        deeplxApiUrl: process.env.DEEPLX_API_URL || 'Not configured',
        deeplxApiKeyConfigured: !!process.env.DEEPLX_API_KEY,
      },
      results,
    });
    
  } catch (error) {
    console.error('Test API error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const { text, locale } = await req.json();
    
    if (!text || !locale) {
      return NextResponse.json({
        success: false,
        error: 'Missing text or locale parameter',
      }, { status: 400 });
    }
    
    const needsTranslation = shouldTranslateToEnglish(locale);
    const isPrimarylyEnglish = isTextPrimarylyEnglish(text);
    const sourceLanguage = getDeepLXLanguageCode(locale);
    
    let result = {
      original: text,
      locale,
      sourceLanguage,
      needsTranslation,
      isPrimarylyEnglish,
      translated: text,
      translationStatus: 'skipped' as string,
      translationTime: 0,
    };
    
    if (needsTranslation && !isPrimarylyEnglish) {
      const startTime = Date.now();
      try {
        const translatedText = await translateText(text, sourceLanguage, 'EN');
        result.translated = translatedText;
        result.translationTime = Date.now() - startTime;
        result.translationStatus = translatedText !== text ? 'success' : 'failed';
      } catch (error) {
        result.translationStatus = 'error';
        console.error('Translation error:', error);
      }
    }
    
    return NextResponse.json({
      success: true,
      result,
    });
    
  } catch (error) {
    console.error('Translation test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
