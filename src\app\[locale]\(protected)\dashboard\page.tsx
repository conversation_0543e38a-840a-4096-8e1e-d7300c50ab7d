'use client';

import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { ImageCard, type ImageCardData } from '@/components/shared/image-card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { authClient } from '@/lib/auth-client';
import { Routes } from '@/routes';
import { Plus, RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

// 图片数据类型
interface GeneratedImage {
  id: string;
  uuid: string;
  userId: string;
  imageUrl: string;
  imageSize: string;
  width: number;
  height: number;
  format: string;
  prompt: string;
  imageDescription?: string;
  modelName: string;
  modelParams?: any;
  generationTime?: number;
  status: number;
  isPublic: boolean;
  slug?: string;
  createdAt: string;
  updatedAt: string;
}

const IMAGES_PER_PAGE = 12;

/**
 * Dashboard main page - now shows user's images
 */
export default function DashboardPage() {
  const t = useTranslations('Dashboard.dashboard');
  const tMyImages = useTranslations('HomePage.dashboard.myImages');
  const { data: session } = authClient.useSession();
  const [images, setImages] = useState<GeneratedImage[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const breadcrumbs = [
    {
      label: t('title'),
      isCurrentPage: true,
    },
  ];

  // 获取用户图片数据
  const fetchMyImages = async (page: number) => {
    if (!session?.user?.id) {
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      setIsLoading(true);

      const response = await fetch(
        `/api/images?page=${page}&limit=${IMAGES_PER_PAGE}&userId=${session.user.id}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setImages(data.images || []);
      setTotal(data.total || 0);
      setCurrentPage(data.page || 1);
      setTotalPages(data.totalPages || 0);
    } catch (error) {
      console.error('Error fetching images:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to load images'
      );
      setImages([]);
      setTotal(0);
      setTotalPages(0);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载 - 等待 session 加载完成
  useEffect(() => {
    if (session?.user?.id) {
      fetchMyImages(1);
    }
  }, [session?.user?.id]);

  // 页面变化时重新加载
  useEffect(() => {
    if (currentPage > 1 && session?.user?.id) {
      fetchMyImages(currentPage);
    }
  }, [currentPage, session?.user?.id]);

  // 处理页面变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      <DashboardHeader breadcrumbs={breadcrumbs} />

      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-6 py-6 md:gap-8 md:py-8 px-4 lg:px-6 xl:px-8">
            {/* 页面标题 */}
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h1 className="text-2xl font-bold tracking-tight">
                  {tMyImages('title')}
                </h1>
                <p className="text-muted-foreground">{tMyImages('subtitle')}</p>
              </div>

              {!isLoading && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{tMyImages('totalImages', { count: total })}</span>
                  {totalPages > 1 && (
                    <span>
                      •{' '}
                      {tMyImages('pageInfo', {
                        current: currentPage,
                        total: totalPages,
                      })}
                    </span>
                  )}
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => fetchMyImages(currentPage)}
                    title={tMyImages('refresh')}
                  >
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* 加载状态 */}
            {isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(IMAGES_PER_PAGE)].map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <Skeleton className="aspect-[4/3] w-full" />
                    <CardContent className="p-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-full mb-2" />
                      <Skeleton className="h-3 w-2/3 mb-4" />
                      <div className="flex gap-2">
                        <Skeleton className="h-8 flex-1" />
                        <Skeleton className="h-8 w-16" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="text-center py-12">
                <p className="text-red-500 mb-4">{error}</p>
                <Button onClick={() => fetchMyImages(currentPage)}>
                  {tMyImages('retry')}
                </Button>
              </div>
            )}

            {/* 空状态 */}
            {!isLoading && !error && images.length === 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
                {/* 生成新图片卡片 - 空状态时显示 */}
                <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] border-2 border-dashed border-muted-foreground/25 hover:border-primary/60 bg-gradient-to-br from-background to-muted/20">
                  <a href={Routes.DashboardGenerateImages} className="block">
                    <div className="aspect-[4/3] flex flex-col items-center justify-center p-6 transition-colors">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center mb-4 group-hover:from-primary/20 group-hover:to-primary/30 transition-all duration-300 shadow-sm">
                        <Plus className="w-8 h-8 text-primary group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <h3 className="font-semibold text-lg text-center mb-2 group-hover:text-primary transition-colors duration-300">
                        {tMyImages('generateNew')}
                      </h3>
                      <p className="text-sm text-muted-foreground text-center leading-relaxed">
                        {tMyImages('generateNewDescription')}
                      </p>
                    </div>
                  </a>
                </Card>
              </div>
            )}

            {/* 图片网格 */}
            {!isLoading && !error && images.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6">
                {/* 生成新图片卡片 */}
                <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] border-2 border-dashed border-muted-foreground/25 hover:border-primary/60 bg-gradient-to-br from-background to-muted/20">
                  <a href={Routes.DashboardGenerateImages} className="block">
                    <div className="aspect-[4/3] flex flex-col items-center justify-center p-6 transition-colors">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center mb-4 group-hover:from-primary/20 group-hover:to-primary/30 transition-all duration-300 shadow-sm">
                        <Plus className="w-8 h-8 text-primary group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <h3 className="font-semibold text-lg text-center mb-2 group-hover:text-primary transition-colors duration-300">
                        {tMyImages('generateNew')}
                      </h3>
                      <p className="text-sm text-muted-foreground text-center leading-relaxed">
                        {tMyImages('generateNewDescription')}
                      </p>
                    </div>
                  </a>
                </Card>

                {/* 现有图片 */}
                {images.map((image) => (
                  <ImageCard
                    key={image.id}
                    image={image as ImageCardData}
                    showFilename={false}
                    showDetailedMetadata={true}
                  />
                ))}
              </div>
            )}

            {/* 分页 */}
            {!isLoading && totalPages > 1 && (
              <div className="mt-8 flex items-center justify-center">
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    onClick={() =>
                      handlePageChange(Math.max(1, currentPage - 1))
                    }
                    disabled={currentPage === 1}
                    size="sm"
                  >
                    {tMyImages('pagination.previous')}
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum: number;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            currentPage === pageNum ? 'default' : 'outline'
                          }
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() =>
                      handlePageChange(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    size="sm"
                  >
                    {tMyImages('pagination.next')}
                  </Button>
                </div>
              </div>
            )}

            {/* 页脚信息 */}
            {!isLoading && images.length > 0 && (
              <div className="text-center text-sm text-muted-foreground mt-8">
                {tMyImages('pagination.showingResults', {
                  start: (currentPage - 1) * IMAGES_PER_PAGE + 1,
                  end: Math.min(currentPage * IMAGES_PER_PAGE, total),
                  total: total,
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
