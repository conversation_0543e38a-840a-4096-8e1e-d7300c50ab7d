CREATE TABLE "generated_image" (
	"id" text PRIMARY KEY NOT NULL,
	"uuid" text NOT NULL,
	"user_id" text NOT NULL,
	"filename" text NOT NULL,
	"image_url" text NOT NULL,
	"image_size" text,
	"width" integer,
	"height" integer,
	"format" text DEFAULT 'PNG' NOT NULL,
	"prompt" text NOT NULL,
	"original_description" text,
	"image_description" text,
	"model_name" text NOT NULL,
	"model_params" json,
	"generation_time" integer,
	"status" integer DEFAULT 1 NOT NULL,
	"is_public" boolean DEFAULT false NOT NULL,
	"slug" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "generated_image_uuid_unique" UNIQUE("uuid"),
	CONSTRAINT "generated_image_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "generated_image" ADD CONSTRAINT "generated_image_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;