{"Metadata": {"name": "<PERSON><PERSON><PERSON>", "title": "Genillu - AI Illustration Generator for Beautiful Notion-Style Art", "description": "Create stunning Notion-style flat illustrations with AI at genillu.com. Perfect for documentation, presentations, and creative projects."}, "Pagination": {"previous": "Previous", "next": "Next", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page", "morePages": "More pages"}, "Common": {"login": "Log in", "logout": "Log out", "signUp": "Sign up", "language": "Switch language", "mode": {"label": "Toggle mode", "light": "Light", "dark": "Dark", "system": "System"}, "theme": {"label": "Toggle theme", "default": "<PERSON><PERSON><PERSON>", "blue": "Blue", "green": "Green", "amber": "Amber", "neutral": "Neutral"}, "copy": "Copy", "saving": "Saving...", "save": "Save", "loading": "Loading...", "cancel": "Cancel", "logoutFailed": "Failed to log out", "table": {"totalRecords": "Total {count} records", "noResults": "No results", "loading": "Loading...", "columns": "Columns", "rowsPerPage": "Rows per page", "page": "Page", "firstPage": "First Page", "lastPage": "Last Page", "nextPage": "Next Page", "previousPage": "Previous Page", "ascending": "Asc", "descending": "Desc"}}, "PricingPage": {"title": "Pricing", "description": "Choose the plan that works best for you", "subtitle": "Choose the plan that works best for you", "monthly": "Monthly", "yearly": "Yearly", "PricingCard": {"freePrice": "$0", "perMonth": "/month", "perYear": "/year", "popular": "Popular", "currentPlan": "Current Plan", "yourCurrentPlan": "Your Current Plan", "getStartedForFree": "Get Started For Free", "getLifetimeAccess": "Get Lifetime Access", "getStarted": "Get Started", "notAvailable": "Not Available", "daysTrial": "{days}-day free trial"}, "CheckoutButton": {"loading": "Loading...", "checkoutFailed": "Failed to open checkout page"}}, "PricePlans": {"free": {"name": "Free", "description": "Basic features for personal use", "features": {"feature-1": "3 credits per month", "feature-2": "Unlimited downloads of illustrations", "feature-3": "High-quality images", "feature-4": "Commercial use allowed"}, "limits": {"limit-1": "Credits expire in 30 days", "limit-2": "All generated images are public by default", "limit-3": "Generate black and white style images"}}, "pro": {"name": "Pro", "description": "Advanced features for professionals", "features": {"feature-1": "30 credits per month", "feature-2": "Commercial use allowed", "feature-3": "Faster generation speed", "feature-4": "Optional colorful style images", "feature-5": "Optional private images"}, "limits": {"limit-1": "Credits expire in 30 days", "limit-2": "All generated images are public by default"}}, "lifetime": {"name": "Lifetime", "description": "100 credits per month for life", "features": {"feature-1": "100 credits per month", "feature-2": "Ultra-high quality images", "feature-3": "Priority generation speed", "feature-4": "Unlimited downloads of illustrations", "feature-5": "Optional colorful style images", "feature-6": "Optional private images", "feature-7": "Lifetime updates"}}}, "CreditPackages": {"basic": {"name": "Basic", "description": "Basic credits package description"}, "standard": {"name": "Standard", "description": "Standard credits package description"}, "premium": {"name": "Premium", "description": "Premium credits package description"}, "enterprise": {"name": "Enterprise", "description": "Enterprise credits package description"}}, "NotFoundPage": {"title": "404", "message": "Sorry, the page you are looking for does not exist.", "backToHome": "Back to home"}, "ErrorPage": {"title": "Oops! Something went wrong!", "tryAgain": "Try again", "backToHome": "Back to home"}, "AboutPage": {"title": "About", "description": "Learn about AI Illustration Generator - born from a passion for Notion's distinctive flat illustration style and a mission to make beautiful, functional design accessible to everyone.", "authorName": "AI Illustration Generator", "authorBio": "Creating Beautiful Notion-Style Illustrations", "introduction": "👋 Hi there! The birth of AI Illustration Generator came from a personal passion and frustration. As a long-time Notion user, I fell in love with their distinctive flat illustration style – clean, minimalist designs with subtle colors that perfectly balance professionalism and creativity.", "ourStory": {"title": "Our Story", "content": "I searched everywhere for similar illustrations to use in my projects, but couldn't find anything that truly captured that unique Notion aesthetic. The options were either too complex, too cartoonish, or simply not aligned with that clean, thoughtful design language I admired.\n\nSo I thought: \"If I can't find it, why not build it?\" With recent advances in AI technology, I saw an opportunity to create a tool that could generate exactly the style of illustrations I was looking for – and share it with others who might be facing the same challenge.\n\nThat's how AI Illustration Generator was born – from a personal need to create Notion-style flat illustrations that are simple yet sophisticated, functional yet beautiful."}, "ourMission": {"title": "Our Mission", "content": "At AI Illustration Generator, we're on a mission to democratize access to beautiful, functional illustrations in the distinctive Notion style. We believe that clean, minimalist visuals shouldn't be limited to those with design skills or big budgets.\n\nOur goal is to provide a platform where anyone – from students and entrepreneurs to product managers and content creators – can generate stunning flat illustrations that enhance their work with just a few clicks.", "quote": "Good design is as little design as possible. Less, but better – because it concentrates on the essential aspects, and the products are not burdened with non-essentials.", "quoteAuthor": "<PERSON> <PERSON><PERSON>"}, "ourTechnology": {"title": "Our Technology", "content": "We've trained our AI specifically on the clean, minimalist aesthetic that defines Notion-style illustrations. Our technology understands the key elements that make this style unique – the perfect balance of simplicity and detail, the thoughtful use of negative space, and the subtle color palettes that complement rather than distract.\n\nUnlike generic AI art generators, our system is fine-tuned to create illustrations that maintain consistent proportions, clean lines, and the functional clarity that makes this style so effective for documentation, presentations, and knowledge bases."}, "ourValues": {"title": "Our Values", "simplicity": {"title": "Simplicity", "content": "We believe in the power of simplicity. Like the Notion aesthetic itself, we strive for clean, uncluttered solutions that focus on what truly matters."}, "accessibility": {"title": "Accessibility", "content": "Great design should be accessible to everyone. We're committed to making beautiful illustrations available regardless of artistic ability or technical skill."}}, "joinOurJourney": {"title": "Join Our Journey", "content": "Join us at AI Illustration Generator and start creating beautiful Notion-style illustrations for your projects. Whether you're designing documentation, building a startup, or just love clean, minimalist aesthetics, our tool is here to help you bring your ideas to life.\n\nHave ideas for how we can improve? Found a perfect use case for your illustrations? We'd love to hear from you!"}, "talkWithMe": "Contact Us", "followMe": "Follow Us"}, "ChangelogPage": {"title": "Changelog", "description": "Stay up to date with the latest changes in our product", "subtitle": "Stay up to date with the latest changes in our product"}, "ContactPage": {"title": "Contact", "description": "We'll help you find the right plan for your business", "subtitle": "We'll help you find the right plan for your business", "form": {"title": "Contact Us", "description": "If you have any questions or feedback, please reach out to our team", "name": "Name", "email": "Email", "message": "Message", "submit": "Submit", "submitting": "Submitting...", "success": "Message sent successfully", "fail": "Failed to send message", "nameMinLength": "Name must be at least 3 characters", "nameMaxLength": "Name must not exceed 30 characters", "emailValidation": "Please enter a valid email address", "messageMinLength": "Message must be at least 10 characters", "messageMaxLength": "Message must not exceed 500 characters"}}, "WaitlistPage": {"title": "Waitlist", "description": "Join our waitlist for the launch of our product", "subtitle": "Join our waitlist for the launch of our product", "form": {"title": "Join Our Waitlist", "description": "We will notify you when we launch our product", "email": "Email", "subscribe": "Subscribe", "subscribing": "Subscribing...", "success": "Subscribed successfully", "fail": "Failed to subscribe", "emailValidation": "Please enter a valid email address"}}, "Newsletter": {"title": "Newsletter", "subtitle": "Join the community", "description": "Subscribe for regular handpicked illustration recommendations.", "form": {"email": "Email", "subscribe": "Subscribe", "subscribing": "Subscribing...", "success": "Subscribed successfully", "fail": "Failed to subscribe", "emailValidation": "Please enter a valid email address"}}, "AuthPage": {"login": {"title": "<PERSON><PERSON>", "welcomeBack": "Welcome back", "email": "Email", "password": "Password", "signIn": "Sign In", "signUpHint": "Don't have an account? Sign up", "forgotPassword": "Forgot Password?", "signInWithGoogle": "Sign In with Google", "signInWithGitHub": "Sign In with GitHub", "showPassword": "Show password", "hidePassword": "Hide password", "or": "Or continue with", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password", "captchaInvalid": "Captcha verification failed", "captchaError": "Captcha verification error"}, "register": {"title": "Register", "createAccount": "Create an account", "name": "Name", "email": "Email", "password": "Password", "signUp": "Sign Up", "signInHint": "Already have an account? Sign in", "checkEmail": "Please check your email inbox", "showPassword": "Show password", "hidePassword": "Hide password", "nameRequired": "Please enter your name", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password", "captchaInvalid": "Captcha verification failed", "captchaError": "Captcha verification error"}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "send": "Send reset link", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox", "emailRequired": "Please enter your email"}, "resetPassword": {"title": "Reset Password", "password": "Password", "reset": "Reset password", "backToLogin": "Back to login", "showPassword": "Show password", "hidePassword": "Hide password", "minLength": "Password must be at least 8 characters"}, "error": {"title": "Oops! Something went wrong!", "tryAgain": "Please try again.", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox"}, "common": {"termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "byClickingContinue": "By clicking continue, you agree to our ", "and": " and "}}, "BlogPage": {"title": "Blog", "description": "Latest news and updates from our team", "subtitle": "Latest news and updates from our team", "author": "Author", "categories": "Categories", "tableOfContents": "Table of Contents", "readTime": "{minutes} min read", "all": "All", "noPostsFound": "No posts found", "allPosts": "All Posts", "morePosts": "More Posts"}, "DocsPage": {"toc": "Table of Contents", "search": "Search docs", "lastUpdate": "Last updated on", "searchNoResult": "No results", "previousPage": "Previous", "nextPage": "Next", "chooseLanguage": "Select language", "title": "AI Illustration Generator Docs", "homepage": "Homepage", "blog": "Blog"}, "Marketing": {"navbar": {"features": {"title": "Features"}, "pricing": {"title": "Pricing"}, "blog": {"title": "Blog"}, "gallery": {"title": "Gallery"}, "docs": {"title": "Docs"}, "ai": {"title": "AI Tools", "items": {"text": {"title": "AI Text", "description": "Show how to use AI to write stunning text"}, "image": {"title": "AI Image", "description": "Show how to use AI to generate beautiful images"}, "video": {"title": "AI Video", "description": "Show how to use AI to generate amazing videos"}, "audio": {"title": "AI Audio", "description": "Show how to use AI to generate wonderful audio"}}}, "pages": {"title": "Pages", "items": {"about": {"title": "About", "description": "Learn more about our company, mission, and values"}, "contact": {"title": "Contact", "description": "Get in touch with our team for support or inquiries"}, "waitlist": {"title": "Waitlist", "description": "Join our waitlist for latest news and updates"}, "changelog": {"title": "Changelog", "description": "See the latest updates to our products"}, "roadmap": {"title": "Roadmap", "description": "Explore our future plans and upcoming features"}, "cookiePolicy": {"title": "<PERSON><PERSON>", "description": "How we use the cookies on our website"}, "privacyPolicy": {"title": "Privacy Policy", "description": "How we protect and handle your data"}, "termsOfService": {"title": "Terms of Service", "description": "The legal agreement between you and our company"}}}, "blocks": {"title": "Blocks", "items": {"magicui": {"title": "MagicUI Blocks"}, "hero-section": {"title": "Hero Blocks"}, "logo-cloud": {"title": "Logo Cloud Blocks"}, "integrations": {"title": "Integrations Blocks"}, "features": {"title": "Features Blocks"}, "content": {"title": "Content Blocks"}, "stats": {"title": "Stats Blocks"}, "team": {"title": "Team Blocks"}, "testimonials": {"title": "Testimonials Blocks"}, "callToAction": {"title": "Call to Action Blocks"}, "footer": {"title": "Footer Blocks"}, "pricing": {"title": "Pricing Blocks"}, "comparator": {"title": "Comparator Blocks"}, "faq": {"title": "FAQ Blocks"}, "login": {"title": "Login Blocks"}, "signup": {"title": "Signup Blocks"}, "forgot-password": {"title": "Forgot Password Blocks"}, "contact": {"title": "Contact Blocks"}}}}, "footer": {"tagline": "Create beautiful Notion-style illustrations with AI, simply and effortlessly", "product": {"title": "Product", "items": {"pricing": "Pricing", "faq": "FAQ"}}, "company": {"title": "Company", "items": {"about": "About", "contact": "Contact"}}, "resources": {"title": "Resources", "items": {"blog": "Blog"}}, "legal": {"title": "Legal", "items": {"cookiePolicy": "<PERSON><PERSON>", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}}}, "avatar": {"dashboard": "Dashboard", "billing": "Billing", "credits": "Credits", "settings": "Settings"}}, "Dashboard": {"dashboard": {"title": "Dashboard"}, "sidebar": {"dashboard": "Dashboard", "generateImages": "Image Generation", "myImages": "My Images"}, "admin": {"title": "Admin", "users": {"title": "Users", "fakeData": "Note: Faked data for demonstration, some features are disabled", "error": "Failed to get users", "search": "Search users...", "columns": {"columns": "Columns", "name": "Name", "email": "Email", "role": "Role", "createdAt": "Created At", "customerId": "Customer ID", "status": "Status", "banReason": "Ban Reason", "banExpires": "Ban Expires"}, "admin": "Admin", "user": "User", "email": {"verified": "<PERSON><PERSON>", "unverified": "Email Unverified"}, "emailCopied": "Email copied to clipboard", "banned": "Banned", "active": "Active", "joined": "Joined at", "updated": "Updated at", "ban": {"reason": "Ban Reason", "reasonPlaceholder": "Enter the reason for banning this user", "defaultReason": "Spamming", "never": "Never", "expires": "Ban Expires", "selectDate": "Select Date", "button": "Ban User", "success": "User has been banned", "error": "Failed to ban user"}, "unban": {"button": "Unban User", "success": "User has been unbanned", "error": "Failed to unban user"}, "close": "Close"}}, "settings": {"title": "Settings", "profile": {"title": "Profile", "description": "Manage your account information", "avatar": {"title": "Avatar", "description": "Click upload button to upload a custom one", "recommendation": "An avatar is optional but strongly recommended", "uploading": "Uploading...", "uploadAvatar": "Upload Avatar", "success": "Avatar updated successfully", "fail": "Failed to update avatar"}, "name": {"title": "Name", "description": "Please enter your display name", "placeholder": "Enter your name", "minLength": "Please use 3 characters at minimum", "maxLength": "Please use 30 characters at maximum", "hint": "Please use 3-30 characters for your name", "success": "Name updated successfully", "fail": "Failed to update name", "saving": "Saving...", "save": "Save"}}, "billing": {"title": "Billing", "description": "Manage your subscription and billing details", "status": {"active": "Active", "trial": "Trial", "free": "Free", "lifetime": "Lifetime"}, "interval": {"month": "month", "year": "year", "oneTime": "one-time"}, "currentPlan": {"title": "Current Plan", "description": "Your current plan details", "noPlan": "You have no active plan"}, "CustomerPortalButton": {"loading": "Loading...", "createCustomerPortalFailed": "Failed to open Stripe customer portal"}, "price": "Price:", "periodStartDate": "Period start date:", "nextBillingDate": "Next billing date:", "trialEnds": "Trial ends:", "freePlanMessage": "You are currently on the free plan with limited features", "lifetimeMessage": "You have lifetime access to all premium features", "manageSubscription": "Manage Subscription and Billing", "manageBilling": "Manage Billing", "upgradePlan": "Upgrade Plan", "retry": "Retry", "errorMessage": "Failed to get data", "paymentSuccess": "Payment successful"}, "credits": {"title": "Credits", "description": "Manage your credit transactions", "balance": {"title": "Credit Balance", "description": "Your credit balance", "credits": "Credits", "creditsDescription": "You have {credits} credits", "creditsExpired": "Credits expired", "creditsAdded": "Credits have been added to your account", "viewTransactions": "View Credit Transactions", "retry": "Retry", "subscriptionCredits": "{credits} credits from subscription this month", "lifetimeCredits": "{credits} credits from lifetime plan this month", "expiringCredits": "{credits} credits expiring on {date}"}, "packages": {"title": "Credit Packages", "description": "Purchase additional credits to use our services", "purchase": "Purchase", "processing": "Processing...", "popular": "Popular", "completePurchase": "Complete Your Purchase", "failedToFetchCredits": "Failed to fetch credits", "failedToCreatePaymentIntent": "Failed to create payment intent", "failedToInitiatePayment": "Failed to initiate payment", "cancel": "Cancel", "purchaseFailed": "Purchase credits failed", "checkoutFailed": "Failed to create checkout session", "loading": "Loading...", "pay": "Pay"}, "transactions": {"title": "Credit Transactions", "error": "Failed to get credit transactions", "search": "Search credit transactions...", "paymentIdCopied": "Payment ID copied to clipboard", "columns": {"columns": "Columns", "id": "ID", "type": "Type", "description": "Description", "amount": "Amount", "remainingAmount": "Remaining Amount", "paymentId": "Payment ID", "expirationDate": "Expiration Date", "expirationDateProcessedAt": "Expiration Date Processed At", "createdAt": "Created At", "updatedAt": "Updated At"}, "types": {"MONTHLY_REFRESH": "Monthly Refresh", "REGISTER_GIFT": "Register Gift", "PURCHASE": "Purchased Credits", "USAGE": "Consumed Credits", "EXPIRE": "Expired Credits", "SUBSCRIPTION_RENEWAL": "Subscription Renewal", "LIFETIME_MONTHLY": "Lifetime Monthly"}, "detailViewer": {"title": "Credit Transaction Detail", "close": "Close"}, "expired": "Expired", "never": "Never"}}, "notification": {"title": "Notification", "description": "Manage your notification preferences", "newsletter": {"title": "Newsletter Subscription", "description": "Manage your newsletter subscription preferences", "label": "Subscribe to newsletter", "hint": "You can change your subscription preferences at any time", "emailRequired": "Email is required to subscribe to the newsletter", "subscribeSuccess": "Successfully subscribed to the newsletter", "subscribeFail": "Failed to subscribe to the newsletter", "unsubscribeSuccess": "Successfully unsubscribed from the newsletter", "unsubscribeFail": "Failed to unsubscribe from the newsletter", "error": "An error occurred while updating your subscription"}}, "security": {"title": "Security", "description": "Manage your security settings", "updatePassword": {"title": "Change Password", "description": "Enter your current password and a new password", "currentPassword": "Current Password", "currentRequired": "Current password is required", "newPassword": "New Password", "newMinLength": "Password must be at least 8 characters", "hint": "Please use at least 8 characters for password", "showPassword": "Show password", "hidePassword": "Hide password", "success": "Password updated successfully", "fail": "Failed to update password", "saving": "Saving...", "save": "Save"}, "resetPassword": {"title": "Reset Password", "description": "Reset password to enable email login", "info": "Resetting your password will allow you to sign in using your email and password in addition to your social login methods. You will receive an email with instructions to reset your password", "button": "Reset Password"}, "deleteAccount": {"title": "Delete Account", "description": "Permanently remove your account and all of its contents", "warning": "This action is not reversible, so please continue with caution", "button": "Delete Account", "confirmTitle": "Delete Account", "confirmDescription": "Are you sure you want to delete your account? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "success": "Account deleted successfully", "fail": "Failed to delete account"}}}, "upgrade": {"title": "Upgrade", "description": "Upgrade to Pro to access all features", "button": "Upgrade"}}, "Mail": {"common": {"team": "{name} Team", "copyright": "©️ {year} All Rights Reserved."}, "verifyEmail": {"title": "Hi, {name}.", "body": "Please click the link below to verify your email address.", "confirmEmail": "Confirm email", "subject": "Verify your email"}, "forgotPassword": {"title": "Hi, {name}.", "body": "Please click the link below to reset your password.", "resetPassword": "Reset password", "subject": "Reset your password"}, "subscribeNewsletter": {"body": "Thank you for subscribing to the newsletter. We will keep you updated with the latest news and updates.", "subject": "Thanks for subscribing"}, "contactMessage": {"name": "Name: {name}", "email": "Email: {email}", "message": "Message: {message}", "subject": "Contact Message from Website"}}, "HomePage": {"cta": {"title": "Experience  AI Illustration Now", "description": "Try for free and quickly generate high-quality Notion-style illustrations to enhance your documents and creations.", "button": "Start for Free", "buttonText": "Start for Free", "viewMore": "View More", "imageAlt": "AI-Powered Notion-style illustration", "features": {"0": "No design experience required, everyone can use it", "1": "AI fast generation, efficiency boost", "2": "High-quality output, suitable for multiple scenarios"}}, "howItWorks": {"title": "How It Works", "description": "Just three steps to easily generate your Notion-style illustration.", "steps": {"0": {"title": "Enter your idea", "description": "Describe the illustration you want in the input box."}, "1": {"title": "AI Smart Generation", "description": "AI will automatically generate a minimalist illustration based on your description."}, "2": {"title": "Download and Use", "description": "Download high-quality illustrations with one click and use them in documents, presentations, and more."}}}, "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Create beautiful Notion-style illustrations with AI, simply and effortlessly", "hero": {"title": "Create Beautiful Notion-Style Illustrations", "description": "Generate clean, minimalist illustrations with our AI-powered tool. Perfect for documentation, presentations, and more.", "introduction": "Introducing AI Illustration Generator", "primary": "Get Started", "secondary": "View Gallery", "rightButton": "Generate"}, "logocloud": {"title": "Your favorite companies are our partners"}, "integration": {"title": "Powerful Features", "subtitle": "Create beautiful Notion-style illustrations with our AI-powered tool", "description": "Discover how our AI illustration generator can transform your creative workflow with advanced features designed for modern content creators.", "learnMore": "Learn More", "items": {"item-1": {"title": "AI-Powered Generation", "description": "Create beautiful Notion-style illustrations with just a text prompt. Our AI understands your needs and delivers perfect results."}, "item-2": {"title": "Clean, Minimalist Style", "description": "Get illustrations with the distinctive Notion aesthetic - clean lines, subtle colors, and thoughtful design elements."}, "item-3": {"title": "Customizable Options", "description": "Simple illustrations in black and white are provided by default, but you can also generate illustrations with colors if you want, whatever you like"}, "item-4": {"title": "Instant Downloads", "description": "Download your illustrations instantly in high-resolution formats, ready to use in your documents, presentations, or websites."}, "item-5": {"title": "Lightning Fast", "description": "Generate illustrations in seconds, not minutes. Our optimized AI delivers results quickly so you can keep working."}, "item-6": {"title": "Perfect for Documentation", "description": "Ideal for enhancing documentation, wikis, and knowledge bases with visuals that complement your content perfectly."}}}, "integration2": {"title": "Integrate with your favorite tools", "description": "Embed the material into your blog or post to enrich the content", "primaryButton": "Get Started", "secondaryButton": "Explore More"}, "features": {"title": "Why Choose Our AI Illustration Generator?", "subtitle": "All the value you need to create, customize, and use Notion-style illustrations.", "imageCaption": "A girl reading a book", "floating1": "Save 95% Time", "floating2": "No Design Skills Required", "button": "Start Creating Now", "items": {"item-1": {"title": "Time-Saving Efficiency", "description": "Generate custom illustrations in seconds instead of hours. What would take a professional designer significant time can be done with just a few clicks."}, "item-2": {"title": "No Design Skills Required", "description": "Our intuitive interface and AI technology handle all the complex design work. Simply describe what you want, and let our system create it for you."}, "item-3": {"title": "Consistent Notion-Style Aesthetic", "description": "All illustrations follow the clean, minimalist Notion style that works perfectly for documentation, presentations, and professional content."}, "item-4": {"title": "Unlimited Customization", "description": "Fine-tune your illustrations with simple text prompts. Create exactly what you need for your specific use case or brand guidelines."}}}, "features2": {"title": "Features2", "subtitle": "The features of your product", "description": "Write the description of your product here", "feature-1": "Product Feature One", "feature-2": "Product Feature Two", "feature-3": "Product Feature Three", "feature-4": "Product Feature Four"}, "features3": {"title": "Features3", "subtitle": "The features of your product", "description": "Write the description of your product here", "items": {"item-1": {"title": "Product Feature One", "description": "Please write the detailed description of feature one here"}, "item-2": {"title": "Product Feature Two", "description": "Please write the detailed description of feature two here"}, "item-3": {"title": "Product Feature Three", "description": "Please write the detailed description of feature three here"}, "item-4": {"title": "Product Feature Four", "description": "Please write the detailed description of feature four here"}, "item-5": {"title": "Product Feature Five", "description": "Please write the detailed description of feature five here"}, "item-6": {"title": "Product Feature Six", "description": "Please write the detailed description of feature six here"}}}, "pricing": {"title": "Pricing", "subtitle": "Pricing", "description": "Choose the plan that works best for you"}, "gallery": {"title": "Gallery", "subtitle": "AI Image Gallery", "description": "Browse beautiful AI-generated artwork", "publicImages": "Public Images", "totalImages": "Total {count} images", "pageInfo": "Page {current}/{total}", "noImages": "No Images", "noImagesDescription": "No public AI-generated images yet", "refresh": "Refresh", "error": "Something went wrong", "retry": "Retry", "pagination": {"previous": "Previous", "next": "Next", "showingResults": "Showing {start}-{end} of {total} images"}}, "dashboard": {"generateImages": {"title": "Image Generation", "breadcrumb": "Image Generation", "generator": {"title": "AI Image Generation", "subtitle": "Enter a description and let AI create unique images for you", "aiPrompt": "AI Prompt", "promptPlaceholder": "Describe the image you want to generate...", "englishInputTip": "English input works better", "multiLanguageTip": "Supports multiple languages - the system will automatically optimize for best results", "quickSuggestions": "Quick Suggestions", "refreshSuggestions": "Refresh Suggestions", "modelSelection": "Model Selection", "publicWork": "Privacy Settings", "publicWorkDescription": "Control whether this image is publicly visible in the gallery", "publicWorkDescriptionLocked": "Free users' works are public by default. Upgrade to Pro to control privacy", "styleSelection": "Image Style", "colorfulStyle": "Colorful", "blackWhiteStyle": "Black & White Minimalist", "styleDescription": "Choose the color style for your image", "styleDescriptionLocked": "Upgrade to a paid plan to use colorful style", "generateButton": "Generate Image", "generating": "Generating...", "performance": "Performance", "quality": "Quality", "performanceDescription": "Faster generation with good quality", "qualityDescription": "Slower generation with better quality", "selectModels": "Select Models", "noModelsSelected": "No models selected", "selectAtLeastOne": "Please select at least one model to generate images", "imageGenerated": "Image Generated Successfully", "generationFailed": "Image Generation Failed", "savingToCloud": "Saving to cloud storage...", "savedToCloud": "Saved to cloud storage", "saveFailed": "Save failed", "viewDetails": "View Details", "fileSize": "Size", "dimensions": "Dimensions", "generationTime": "Generation Time", "imageUrl": "URL", "preview": "Preview", "download": "Download", "share": "Share", "copyLink": "Copy Link", "favorite": "Favorite", "clickToViewDetails": "Click to view details", "clickToViewDetailsWhenSaved": "Click to view details in new tab", "imageSaving": "Saving image...", "noImage": "No Image", "noImageDescription": "Enter a description and click generate to start creating", "currentPrompt": "Current Prompt", "generationTimeSeconds": "Generation Time: {seconds} seconds", "imageGenerationComplete": "Image Generation Complete"}}, "myImages": {"title": "My Images", "subtitle": "Manage and view all your generated AI images", "breadcrumb": "My Images", "totalImages": "Total {count} images", "pageInfo": "Page {current}/{total}", "noImages": "You haven't generated any images yet", "startGenerating": "Start Generating Images", "retry": "Retry", "refresh": "Refresh", "public": "Public", "private": "Private", "viewDetails": "View Details", "copyUrl": "Copy Link", "copyMarkdown": "<PERSON><PERSON>", "copied": "Copied!", "download": "Download", "pagination": {"previous": "Previous", "next": "Next", "showingResults": "Showing {start}-{end} of {total} images"}, "generateNew": "Generate New Image", "generateNewDescription": "Create amazing AI artwork"}}, "imageDetail": {"notFound": "Image Not Found", "notFoundDescription": "Sorry, the image you are looking for does not exist or has been deleted.", "backToGallery": "Back to Gallery", "imageInfo": "Image Information", "prompt": "Prompt", "model": "Model", "createdAt": "Created", "fileSize": "File Size", "dimensions": "Dimensions", "format": "Format", "generationTime": "Generation Time", "download": "Download", "copyUrl": "Copy URL", "copyMarkdown": "<PERSON><PERSON>", "copied": "Copied!", "publicImage": "Public", "privateImage": "Private", "generatedImage": "Generated Image", "viewDetails": "View Details", "public": "Public", "private": "Private", "creatorInfo": "Creator Information", "creationTime": "Creation Time", "creationPrompt": "Creation Prompt", "copyPrompt": "Copy Prompt", "copiedPrompt": "Copied Prompt", "imageDescription": "Image Description", "modelInfo": "Model Information", "modelParams": "Model Parameters", "seconds": "seconds", "actions": "Actions"}, "faqs": {"title": "FAQ", "subtitle": "Frequently Asked Questions", "items": {"item-1": {"question": "What kind of illustrations can I create?", "answer": "You can create a wide range of Notion-style illustrations including people working. Some users have created interesting animal illustrations. You can browse our gallery to see other people's work."}, "item-2": {"question": "How many illustrations can I generate?", "answer": "Our free tier allows you to generate 3 illustrations. Need unlimited illustrations? Check out our flexible plans—pay only for what you use."}, "item-3": {"question": "Why do some illustrations look strange?", "answer": "Our illustrations are all generated by AI models, so if AI understands incorrectly, there may be unreasonable results. For this part of the content, you can write an email to tell me that I will unconditionally refund you."}, "item-4": {"question": "Can I customize the illustrations?", "answer": "Sorry, it is currently not supported to edit the generated illustrations . We are speeding up research and development."}, "item-5": {"question": "How do I get more generation credits?", "answer": "You can earn additional free generation credits by sharing your AI-generated work on public social media and emailing the reference <NAME_EMAIL>."}}}, "testimonials": {"title": "Testimonials", "subtitle": "What our customers are saying", "items": {"item-1": {"name": "<PERSON>", "role": "Technical Writer", "image": "https://randomuser.me/api/portraits/men/21.jpg", "quote": "This tool has completely transformed how I create documentation. The illustrations are beautiful and perfectly match the Notion aesthetic I was looking for."}, "item-2": {"name": "<PERSON>", "role": "Knowledge Base Manager", "image": "https://randomuser.me/api/portraits/women/22.jpg", "quote": "I've tried many illustration generators, but this one stands out for its clean, minimalist style. Perfect for my knowledge base and documentation."}, "item-3": {"name": "<PERSON>", "role": "Product Designer", "image": "https://randomuser.me/api/portraits/men/23.jpg", "quote": "The speed and quality are impressive. I can generate professional illustrations in seconds that would have taken hours to create manually."}, "item-4": {"name": "<PERSON>", "role": "Content Strategist", "image": "https://randomuser.me/api/portraits/women/24.jpg", "quote": "The user experience is fantastic. I love how easy it is to generate and download illustrations for my articles."}, "item-5": {"name": "<PERSON>", "role": "Developer Advocate", "image": "https://randomuser.me/api/portraits/men/25.jpg", "quote": "This tool saves me so much time when preparing technical documentation. The results always look professional."}, "item-6": {"name": "<PERSON>", "role": "Project Manager", "image": "https://randomuser.me/api/portraits/women/26.jpg", "quote": "I appreciate the minimalist style and the variety of illustration options. It fits perfectly with our brand."}}}, "stats": {"title": "Stats", "subtitle": "Our achievements", "description": "Our Notion style illustration generator has achieved the following:", "items": {"item-1": {"title": "Illustrations Generated"}, "item-2": {"title": "Happy Users"}, "item-3": {"title": "User Rating"}}}, "calltoaction": {"title": "Start Creating", "description": "Create beautiful Notion-style illustrations with our AI-powered generator, simply and effortlessly", "primaryButton": "Get Started", "secondaryButton": "See <PERSON><PERSON>"}}}