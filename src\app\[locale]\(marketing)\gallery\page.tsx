import { GalleryClient } from '@/components/gallery/gallery-client';
import { HeaderSection } from '@/components/layout/header-section';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });
  const gt = await getTranslations({ locale, namespace: 'HomePage.gallery' });

  return constructMetadata({
    title: `${gt('title')} | ${t('title')}`,
    description: gt('description'),
    canonicalUrl: getUrlWithLocale('/gallery', locale),
  });
}

interface GalleryPageProps {
  params: Promise<{ locale: Locale }>;
}
export default async function GalleryPage({ params }: GalleryPageProps) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'HomePage.gallery' });

  return (
    <div className="min-h-screen bg-background">
      <HeaderSection title={t('title')} description={t('subtitle')} />

      <GalleryClient currentPage={1} />
    </div>
  );
}
