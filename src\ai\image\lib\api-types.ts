import type { ProviderKey } from './provider-config';

export interface GenerateImageRequest {
  prompt: string; // 用于 AI 生成的完整 prompt
  originalPrompt: string; // 用户的原始输入，用于数据库存储
  provider: ProviderKey;
  modelId: string;
  isPublic?: boolean;
}

export interface GenerateImageResponse {
  image?: string;
  error?: string;
  provider?: ProviderKey;
  savedImage?: SavedImageData;
  saveError?: string;
}

export interface SavedImageData {
  id: string;
  uuid: string;
  userId: string;
  imageUrl: string;
  imageSize: string;
  width: number;
  height: number;
  format: string;
  prompt: string;
  imageDescription?: string | null;
  modelName: string;
  modelParams?: any;
  generationTime: number;
  status: number;
  isPublic: boolean;
  slug: string;
  createdAt: string;
  updatedAt: string;
}
