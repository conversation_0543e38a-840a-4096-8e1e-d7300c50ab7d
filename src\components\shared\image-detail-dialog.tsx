'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Check,
  Clock,
  Copy,
  Download,
  ExternalLink,
  FileText,
  Globe,
  Lock,
  Palette,
  Settings,
  Sparkles,
  User,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

// 通用图片数据类型
export interface ImageDetailData {
  id: string;
  filename: string;
  imageUrl: string;
  title?: string;
  prompt: string;
  imageDescription?: string;
  originalDescription?: string;
  modelName: string;
  modelParams?: any;
  generationTime?: number;
  status?: number;
  isPublic?: boolean;
  slug?: string;
  createdAt: string;
  updatedAt?: string;
  // 可选的创作者信息（用于 Gallery）
  creator?: {
    name: string;
    avatar?: string;
    username?: string;
  };
  // 可选的文件信息
  imageSize?: string;
  width?: number;
  height?: number;
  format?: string;
}

interface ImageDetailDialogProps {
  image: ImageDetailData;
  trigger: React.ReactNode;
  showCreator?: boolean; // 是否显示创作者信息
  showModelInfo?: boolean; // 是否显示模型信息
  showPrivacyStatus?: boolean; // 是否显示公开/私有状态
}

export function ImageDetailDialog({
  image,
  trigger,
  showCreator = false,
  showModelInfo = false, // 默认隐藏模型信息
  showPrivacyStatus = true,
}: ImageDetailDialogProps) {
  const t = useTranslations('HomePage.dashboard.myImages');
  const [copiedStates, setCopiedStates] = useState<{ [key: string]: boolean }>(
    {}
  );

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates((prev) => ({ ...prev, [type]: true }));
      setTimeout(() => {
        setCopiedStates((prev) => ({ ...prev, [type]: false }));
      }, 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const downloadImage = async (url: string, filename: string) => {
    try {
      // 使用 API 路由代理下载，避免 CORS 问题
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: url }),
      });

      if (!response.ok) {
        throw new Error('Download failed');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (err) {
      console.error('Failed to download: ', err);
      // 如果 API 路由失败，尝试直接下载（可能会有 CORS 问题）
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackErr) {
        console.error('Fallback download also failed: ', fallbackErr);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  const formatModelName = (modelName: string) => {
    return modelName.split('/').pop() || modelName;
  };

  const getMarkdownUrl = () => {
    const title = image.title || image.originalDescription || '生成图片';
    return `![${title}](${image.imageUrl})`;
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold">
            {image.title || image.originalDescription || '图片详情'}
          </DialogTitle>
        </DialogHeader>

        <div className="grid lg:grid-cols-2 gap-8 overflow-y-auto max-h-[calc(90vh-8rem)]">
          {/* 图片预览区域 */}
          <div className="space-y-6">
            <div className="relative bg-muted rounded-lg overflow-hidden border flex items-center justify-center min-h-[400px]">
              <Image
                src={image.imageUrl || '/placeholder.svg'}
                alt={image.prompt}
                width={image.width || 1024}
                height={image.height || 1024}
                className="object-contain max-w-full max-h-full"
                sizes="(max-width: 1024px) 100vw, 50vw"
              />
              {/* 公开/私有状态标识 */}
              {showPrivacyStatus && image.isPublic !== undefined && (
                <div className="absolute top-3 right-3">
                  <Badge
                    variant={image.isPublic ? 'default' : 'secondary'}
                    className="text-xs shadow-sm"
                  >
                    {image.isPublic ? (
                      <>
                        <Globe className="w-3 h-3 mr-1.5" />
                        {t('public')}
                      </>
                    ) : (
                      <>
                        <Lock className="w-3 h-3 mr-1.5" />
                        {t('private')}
                      </>
                    )}
                  </Badge>
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              {/* 查看详情页面按钮 */}
              {image.slug && (
                <Link href={`/image/${image.slug}`} target="_blank">
                  <Button variant="default" size="sm" className="w-full h-10">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    {t('viewDetails')}
                  </Button>
                </Link>
              )}

              {/* 其他操作按钮 */}
              <div className="grid grid-cols-3 gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => downloadImage(image.imageUrl, image.filename)}
                  className="h-10"
                >
                  <Download className="w-4 h-4 mr-2" />
                  {t('download')}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(image.imageUrl, 'url')}
                  className="h-10"
                >
                  {copiedStates.url ? (
                    <>
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      {t('copied')}
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4 mr-2" />
                      {t('copyUrl')}
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(getMarkdownUrl(), 'md')}
                  className="h-10"
                >
                  {copiedStates.md ? (
                    <>
                      <Check className="w-4 h-4 mr-2 text-green-600" />
                      {t('copied')}
                    </>
                  ) : (
                    <>
                      <FileText className="w-4 h-4 mr-2" />
                      {t('copyMarkdown')}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* 详情信息区域 */}
          <div className="space-y-6">
            {/* 创作者信息 */}
            {showCreator && image.creator && (
              <div className="space-y-3">
                <h4 className="font-semibold text-foreground flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  创作者信息
                </h4>
                <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg border">
                  <Avatar className="w-10 h-10">
                    <AvatarImage
                      src={image.creator.avatar || '/placeholder.svg'}
                      alt={image.creator.name}
                    />
                    <AvatarFallback className="text-sm font-medium">
                      {image.creator.name
                        .split(' ')
                        .map((n) => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-foreground">
                      {image.creator.name}
                    </div>
                    {image.creator.username && (
                      <div className="text-sm text-muted-foreground">
                        @{image.creator.username}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 创作时间 */}
            <div className="space-y-3">
              <h4 className="font-semibold text-foreground flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                创作时间
              </h4>
              <div className="p-4 bg-muted/50 rounded-lg border">
                <p className="text-sm text-foreground">
                  {formatDate(image.createdAt)}
                </p>
              </div>
            </div>

            {/* 提示词 */}
            <div className="space-y-3">
              <h4 className="font-semibold text-foreground flex items-center gap-2">
                <Sparkles className="w-4 h-4 text-muted-foreground" />
                创作提示词
              </h4>
              <div className="bg-muted/50 p-4 rounded-lg border space-y-3">
                <p className="text-sm leading-relaxed text-foreground">
                  {image.prompt}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-3 -ml-1"
                  onClick={() => copyToClipboard(image.prompt, 'prompt')}
                >
                  {copiedStates.prompt ? (
                    <>
                      <Check className="w-3 h-3 mr-1.5 text-green-600" />
                      已复制提示词
                    </>
                  ) : (
                    <>
                      <Copy className="w-3 h-3 mr-1.5" />
                      复制提示词
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* 图片描述 */}
            {image.imageDescription && (
              <div className="space-y-3">
                <h4 className="font-semibold text-foreground flex items-center gap-2">
                  <Palette className="w-4 h-4 text-muted-foreground" />
                  图片描述
                </h4>
                <div className="p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
                  <p className="text-sm text-foreground leading-relaxed">
                    {image.imageDescription}
                  </p>
                </div>
              </div>
            )}

            {/* 模型信息 */}
            {showModelInfo && (
              <div className="space-y-3">
                <h4 className="font-semibold text-foreground flex items-center gap-2">
                  <Settings className="w-4 h-4 text-muted-foreground" />
                  模型信息
                </h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                      <span className="text-sm text-muted-foreground">
                        模型
                      </span>
                      <span className="font-mono text-xs text-foreground font-medium">
                        {formatModelName(image.modelName)}
                      </span>
                    </div>
                    {image.generationTime && (
                      <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                        <span className="text-sm text-muted-foreground">
                          生成时间
                        </span>
                        <span className="text-sm text-foreground font-medium">
                          {image.generationTime}秒
                        </span>
                      </div>
                    )}
                    {image.width && image.height && (
                      <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                        <span className="text-sm text-muted-foreground">
                          尺寸
                        </span>
                        <span className="text-sm text-foreground font-medium">
                          {image.width} × {image.height}
                        </span>
                      </div>
                    )}
                    {image.imageSize && (
                      <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                        <span className="text-sm text-muted-foreground">
                          文件大小
                        </span>
                        <span className="text-sm text-foreground font-medium">
                          {image.imageSize}
                        </span>
                      </div>
                    )}
                    {image.format && (
                      <div className="flex justify-between items-center p-3 bg-muted/50 rounded-lg border">
                        <span className="text-sm text-muted-foreground">
                          格式
                        </span>
                        <span className="text-sm text-foreground font-medium">
                          {image.format}
                        </span>
                      </div>
                    )}
                  </div>
                  {image.modelParams && (
                    <div className="space-y-2">
                      <span className="text-sm text-muted-foreground font-medium">
                        模型参数
                      </span>
                      <pre className="text-xs bg-muted/50 p-3 rounded-lg border overflow-x-auto font-mono text-foreground">
                        {JSON.stringify(image.modelParams, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
