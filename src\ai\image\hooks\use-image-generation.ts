import { useCredits } from '@/hooks/use-credits';
import { useState } from 'react';
import type { GenerateImageResponse } from '../lib/api-types';
import type {
  ImageError,
  ImageResult,
  ProviderTiming,
} from '../lib/image-types';
import {
  type ProviderKey,
  initializeProviderRecord,
} from '../lib/provider-config';

interface UseImageGenerationReturn {
  images: ImageResult[];
  errors: ImageError[];
  timings: Record<ProviderKey, ProviderTiming>;
  failedProviders: ProviderKey[];
  isLoading: boolean;
  startGeneration: (
    originalPrompt: string,
    generationPrompt: string,
    providers: ProviderKey[],
    providerToModel: Record<ProviderKey, string>,
    isPublic?: boolean
  ) => Promise<void>;
  resetState: () => void;
  activePrompt: string;
}

export function useImageGeneration(): UseImageGenerationReturn {
  const [images, setImages] = useState<ImageResult[]>([]);
  const [errors, setErrors] = useState<ImageError[]>([]);
  const [timings, setTimings] = useState<Record<ProviderKey, ProviderTiming>>(
    initializeProviderRecord<ProviderTiming>()
  );
  const [failedProviders, setFailedProviders] = useState<ProviderKey[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activePrompt, setActivePrompt] = useState('');
  const { hasEnoughCredits, fetchCredits, balance } = useCredits();

  const resetState = () => {
    setImages([]);
    setErrors([]);
    setTimings(initializeProviderRecord<ProviderTiming>());
    setFailedProviders([]);
    setIsLoading(false);
  };

  const startGeneration = async (
    originalPrompt: string,
    generationPrompt: string,
    providers: ProviderKey[],
    providerToModel: Record<ProviderKey, string>,
    isPublic = false
  ) => {
    setActivePrompt(originalPrompt); // 显示用户的原始输入

    // 检查积分是否足够（每个provider需要1积分）
    const requiredCredits = providers.length;
    console.log(
      `Credit check: required=${requiredCredits}, balance=${balance}, hasEnough=${hasEnoughCredits(requiredCredits)}`
    );

    if (!hasEnoughCredits(requiredCredits)) {
      const error = `Insufficient credits. You need ${requiredCredits} credit(s) but only have ${balance} available. Please purchase more credits to continue.`;
      console.error('Credit check failed:', error);
      setErrors([
        {
          provider: providers[0], // 使用第一个provider作为错误显示
          message: error,
        },
      ]);
      return;
    }

    try {
      setIsLoading(true);
      // Initialize images array with null values
      setImages(
        providers.map((provider) => ({
          provider,
          image: null,
          modelId: providerToModel[provider],
        }))
      );

      // Clear previous state
      setErrors([]);
      setFailedProviders([]);

      // Initialize timings with start times
      const now = Date.now();
      setTimings(
        Object.fromEntries(
          providers.map((provider) => [provider, { startTime: now }])
        ) as Record<ProviderKey, ProviderTiming>
      );

      // Helper to fetch a single provider
      const generateImage = async (provider: ProviderKey, modelId: string) => {
        const startTime = now;
        console.log(
          `Generate image request [provider=${provider}, modelId=${modelId}]`
        );
        try {
          const request = {
            prompt: generationPrompt, // 用于 AI 生成
            originalPrompt, // 用于数据库存储
            provider,
            modelId,
            isPublic,
          };

          const response = await fetch('/api/generate-images', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request),
          });

          console.log(`API Response [provider=${provider}]:`, {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
          });

          const data = (await response.json()) as GenerateImageResponse;
          console.log(`API Response Data [provider=${provider}]:`, data);

          if (!response.ok) {
            const errorMessage =
              data.error || `Server error: ${response.status}`;
            console.error(`API Error [provider=${provider}]:`, errorMessage);
            throw new Error(errorMessage);
          }

          const completionTime = Date.now();
          const elapsed = completionTime - startTime;
          setTimings((prev) => ({
            ...prev,
            [provider]: {
              startTime,
              completionTime,
              elapsed,
            },
          }));

          console.log(
            `Successful image response [provider=${provider}, modelId=${modelId}, elapsed=${elapsed}ms]`
          );

          // Update image in state
          setImages((prevImages) =>
            prevImages.map((item) =>
              item.provider === provider
                ? {
                    ...item,
                    image: data.image ?? null,
                    modelId,
                    savedImage: data.savedImage,
                    saveError: data.saveError,
                  }
                : item
            )
          );

          // 刷新积分余额（图片生成成功后积分已被扣除）
          fetchCredits(true);
        } catch (err) {
          console.error(
            `Error [provider=${provider}, modelId=${modelId}]:`,
            err
          );
          setFailedProviders((prev) => [...prev, provider]);
          setErrors((prev) => [
            ...prev,
            {
              provider,
              message:
                err instanceof Error
                  ? err.message
                  : 'An unexpected error occurred',
            },
          ]);

          setImages((prevImages) =>
            prevImages.map((item) =>
              item.provider === provider
                ? { ...item, image: null, modelId }
                : item
            )
          );
        }
      };

      // Generate images for all active providers
      const fetchPromises = providers.map((provider) => {
        const modelId = providerToModel[provider];
        return generateImage(provider, modelId);
      });

      await Promise.all(fetchPromises);
    } catch (error) {
      console.error('Error fetching images:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    images,
    errors,
    timings,
    failedProviders,
    isLoading,
    startGeneration,
    resetState,
    activePrompt,
  };
}
