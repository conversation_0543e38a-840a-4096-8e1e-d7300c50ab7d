/**
 * 语言代码映射工具
 * 
 * 将项目的多语言代码映射到DeepLX支持的语言代码
 */

/**
 * 项目多语言代码到DeepLX支持的语言代码映射
 * https://deeplx.owo.network/endpoints/official.html#supported-languages
 */
export const LOCALE_TO_DEEPLX_MAPPING: Record<string, string> = {
  'en': 'EN',      // 英语
  'zh': 'ZH',      // 中文
  'ja': 'JA',      // 日语
  'ko': 'KO',      // 韩语
  'fr': 'FR',      // 法语
  'de': 'DE',      // 德语
  'es': 'ES',      // 西班牙语
  'it': 'IT',      // 意大利语
  'pt': 'PT',      // 葡萄牙语
  'ru': 'RU',      // 俄语
  'nl': 'NL',      // 荷兰语
  'pl': 'PL',      // 波兰语
  'sv': 'SV',      // 瑞典语
  'da': 'DA',      // 丹麦语
  'fi': 'FI',      // 芬兰语
  'no': 'NB',      // 挪威语
  'cs': 'CS',      // 捷克语
  'sk': 'SK',      // 斯洛伐克语
  'sl': 'SL',      // 斯洛文尼亚语
  'et': 'ET',      // 爱沙尼亚语
  'lv': 'LV',      // 拉脱维亚语
  'lt': 'LT',      // 立陶宛语
  'bg': 'BG',      // 保加利亚语
  'hu': 'HU',      // 匈牙利语
  'ro': 'RO',      // 罗马尼亚语
  'el': 'EL',      // 希腊语
  'tr': 'TR',      // 土耳其语
  'uk': 'UK',      // 乌克兰语
  'ar': 'AR',      // 阿拉伯语
  'hi': 'HI',      // 印地语
  'th': 'TH',      // 泰语
  'vi': 'VI',      // 越南语
  'id': 'ID',      // 印尼语
  'ms': 'MS',      // 马来语
};

/**
 * 根据项目的locale获取DeepLX支持的语言代码
 * @param locale 项目的语言代码 (如: 'zh', 'ja', 'en')
 * @returns DeepLX支持的语言代码 (如: 'ZH', 'JA', 'EN')，不支持的语言返回 'AUTO'
 */
export function getDeepLXLanguageCode(locale: string): string {
  // 处理带有地区代码的locale (如: zh-CN -> zh)
  const baseLocale = locale.toLowerCase().split('-')[0];
  
  return LOCALE_TO_DEEPLX_MAPPING[baseLocale] || 'AUTO';
}

/**
 * 检查某个locale是否需要翻译为英语
 * @param locale 项目的语言代码
 * @returns 如果不是英语则返回true，需要翻译
 */
export function shouldTranslateToEnglish(locale: string): boolean {
  const baseLocale = locale.toLowerCase().split('-')[0];
  return baseLocale !== 'en';
}

/**
 * 获取支持的所有语言代码列表
 * @returns 支持的语言代码数组
 */
export function getSupportedLocales(): string[] {
  return Object.keys(LOCALE_TO_DEEPLX_MAPPING);
}
