import { randomUUID } from 'crypto';
import { s3mini } from 's3mini';
import { storageConfig } from '../config/storage-config';
import {
  ConfigurationError,
  type StorageConfig,
  StorageError,
  type StorageProvider,
  UploadError,
  type UploadFileParams,
  type UploadFileResult,
} from '../types';

/**
 * Amazon S3 storage provider implementation using s3mini
 *
 * docs:
 * https://mksaas.com/docs/storage
 *
 * This provider works with Amazon S3 and compatible services like Cloudflare R2
 * using s3mini for better Cloudflare Workers compatibility
 * https://github.com/good-lly/s3mini
 * https://developers.cloudflare.com/r2/
 */
export class S3Provider implements StorageProvider {
  private config: StorageConfig;
  private s3Client: s3mini | null = null;

  constructor(config: StorageConfig = storageConfig) {
    this.config = config;
  }

  /**
   * Get the provider name
   */
  public getProviderName(): string {
    return 'S3';
  }

  /**
   * Get the S3 client instance
   */
  private getS3Client(): s3mini {
    if (this.s3Client) {
      return this.s3Client;
    }

    const { region, endpoint, accessKeyId, secretAccessKey, bucketName } =
      this.config;

    if (!region) {
      throw new ConfigurationError('Storage region is not configured');
    }

    if (!accessKeyId || !secretAccessKey) {
      throw new ConfigurationError('Storage credentials are not configured');
    }

    if (!endpoint) {
      throw new ConfigurationError('Storage endpoint is required for s3mini');
    }

    if (!bucketName) {
      throw new ConfigurationError('Storage bucket name is not configured');
    }

    // s3mini client configuration for Cloudflare R2
    // For s3mini, the bucket name should be included in the endpoint
    const { publicUrl } = this.config;

    // Construct the full endpoint with bucket for s3mini
    const s3Endpoint = `${endpoint.replace(/\/$/, '')}/${bucketName}`;

    console.log('S3Provider: Initializing s3mini client for Cloudflare R2');
    console.log('S3Provider: API endpoint:', endpoint);
    console.log('S3Provider: Full S3 endpoint (with bucket):', s3Endpoint);
    console.log('S3Provider: Public URL:', publicUrl || 'NOT SET');
    console.log('S3Provider: Bucket name:', bucketName);
    console.log('S3Provider: Region:', region);
    console.log('S3Provider: Access key ID:', accessKeyId ? 'SET' : 'NOT SET');

    this.s3Client = new s3mini({
      accessKeyId,
      secretAccessKey,
      endpoint: s3Endpoint, // Use endpoint with bucket name
      region,
    });

    return this.s3Client;
  }

  /**
   * Generate a unique filename with the original extension
   */
  private generateUniqueFilename(originalFilename: string): string {
    // 如果文件名看起来已经是唯一的（包含点且没有空格），直接使用
    if (
      originalFilename &&
      originalFilename.includes('.') &&
      !originalFilename.includes(' ')
    ) {
      return originalFilename;
    }

    // 否则生成新的唯一文件名
    const extension = originalFilename.split('.').pop() || '';
    const uuid = randomUUID();
    return `${uuid}${extension ? `.${extension}` : ''}`;
  }

  /**
   * Upload a file to S3
   */
  public async uploadFile(params: UploadFileParams): Promise<UploadFileResult> {
    try {
      const { file, filename, contentType, folder } = params;
      const s3 = this.getS3Client();

      const uniqueFilename = this.generateUniqueFilename(filename);
      // The key should include the bucket for R2 operations
      const key = folder ? `${folder}/${uniqueFilename}` : uniqueFilename;
      const { publicUrl, bucketName } = this.config;

      // Convert Blob to Buffer if needed
      let fileContent: Buffer | string;
      if (file instanceof Blob) {
        fileContent = Buffer.from(await file.arrayBuffer());
      } else {
        fileContent = file;
      }

      // Upload the file using s3mini to the specified bucket
      console.log('S3Provider: Attempting upload with s3mini');
      console.log('S3Provider: Bucket:', bucketName);
      console.log('S3Provider: Key:', key);
      console.log('S3Provider: Content type:', contentType);
      console.log(
        'S3Provider: File size:',
        fileContent instanceof Buffer ? fileContent.length : 'unknown'
      );

      // For s3mini with bucket in endpoint, use key directly without prepending bucket name
      const response = await s3.putObject(key, fileContent, contentType);

      console.log('S3Provider: Upload response status:', response.status);
      console.log('S3Provider: Upload response ok:', response.ok);
      console.log(
        'S3Provider: Upload response statusText:',
        response.statusText
      );

      if (!response.ok) {
        // 尝试读取响应体以获取更多错误信息
        let errorBody = '';
        try {
          errorBody = await response.text();
          console.log('S3Provider: Error response body:', errorBody);
        } catch (e) {
          console.log('S3Provider: Could not read error response body');
        }
        throw new UploadError(
          `Failed to upload file: ${response.status} ${response.statusText} - ${errorBody}`
        );
      }

      // Generate the URL for Cloudflare R2
      let url: string;

      if (publicUrl) {
        // Use custom domain if provided
        url = `${publicUrl.replace(/\/$/, '')}/${key}`;
        console.log('uploadFile, public url', url);
      } else {
        // For Cloudflare R2, construct URL with endpoint + key
        const baseUrl = this.config.endpoint?.replace(/\/$/, '') || '';
        url = `${baseUrl}/${key}`;
        console.log('uploadFile, constructed url', url);
      }

      return { url, key };
    } catch (error) {
      if (error instanceof ConfigurationError) {
        console.error('uploadFile, configuration error', error);
        throw error;
      }

      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error occurred during file upload';
      console.error('uploadFile, error', message);
      throw new UploadError(message);
    }
  }

  /**
   * Delete a file from S3
   */
  public async deleteFile(key: string): Promise<void> {
    try {
      const s3 = this.getS3Client();

      const wasDeleted = await s3.deleteObject(key);

      if (!wasDeleted) {
        console.warn(
          `File with key ${key} was not found or could not be deleted`
        );
      }
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error occurred during file deletion';
      console.error('deleteFile, error', message);
      throw new StorageError(message);
    }
  }
}
